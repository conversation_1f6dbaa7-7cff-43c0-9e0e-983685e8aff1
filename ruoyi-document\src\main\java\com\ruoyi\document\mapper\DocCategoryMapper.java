package com.ruoyi.document.mapper;

import java.util.List;
import com.ruoyi.document.domain.DocCategory;

/**
 * 文档分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface DocCategoryMapper 
{
    /**
     * 查询文档分类
     * 
     * @param categoryId 文档分类主键
     * @return 文档分类
     */
    public DocCategory selectDocCategoryByCategoryId(Long categoryId);

    /**
     * 查询文档分类列表
     * 
     * @param docCategory 文档分类
     * @return 文档分类集合
     */
    public List<DocCategory> selectDocCategoryList(DocCategory docCategory);

    /**
     * 新增文档分类
     * 
     * @param docCategory 文档分类
     * @return 结果
     */
    public int insertDocCategory(DocCategory docCategory);

    /**
     * 修改文档分类
     * 
     * @param docCategory 文档分类
     * @return 结果
     */
    public int updateDocCategory(DocCategory docCategory);

    /**
     * 删除文档分类
     * 
     * @param categoryId 文档分类主键
     * @return 结果
     */
    public int deleteDocCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除文档分类
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDocCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 根据分类ID查询子分类数量
     *
     * @param categoryId 分类ID
     * @return 子分类数量
     */
    public int selectChildrenCountById(Long categoryId);

    /**
     * 检查分类名称是否唯一
     * 
     * @param docCategory 分类信息
     * @return 结果
     */
    public DocCategory checkCategoryNameUnique(DocCategory docCategory);

    /**
     * 根据角色ID查询分类权限
     * 
     * @param roleId 角色ID
     * @return 分类ID列表
     */
    public List<Long> selectCategoryListByRoleId(Long roleId);

    /**
     * 根据用户ID查询分类权限
     *
     * @param userId 用户ID
     * @return 分类ID列表
     */
    public List<Long> selectCategoryListByUserId(Long userId);

    /**
     * 根据分类ID查询子分类
     *
     * @param categoryId 分类ID
     * @return 子分类列表
     */
    public List<DocCategory> selectChildrenCategoryById(Long categoryId);

    /**
     * 批量修改子分类信息
     *
     * @param categories 分类列表
     * @return 结果
     */
    public int updateCategoryChildren(List<DocCategory> categories);
}
