-- 文档管理系统初始化数据脚本
-- 请在VS Code的Database扩展中执行此脚本

USE `ry-vue`;

-- 1. 插入初始化分类数据
INSERT INTO `doc_category` VALUES 
(1000, 0, '0', '技术文档', 'TECH_DOC', 1, '技术相关文档分类', 'documentation', '0', '0', 'admin', NOW(), '', NULL, '技术文档根分类'),
(1001, 1000, '0,1000', '开发文档', 'DEV_DOC', 1, '开发相关文档', 'code', '0', '0', 'admin', NOW(), '', NULL, '开发文档'),
(1002, 1000, '0,1000', '运维文档', 'OPS_DOC', 2, '运维相关文档', 'monitor', '0', '0', 'admin', NOW(), '', NULL, '运维文档'),
(1003, 1000, '0,1000', 'API文档', 'API_DOC', 3, 'API接口文档', 'link', '0', '0', 'admin', NOW(), '', NULL, 'API文档'),
(1004, 0, '0', '管理文档', 'MGMT_DOC', 2, '管理制度文档', 'system', '0', '0', 'admin', NOW(), '', NULL, '管理文档根分类'),
(1005, 1004, '0,1004', '制度规范', 'RULE_DOC', 1, '公司制度规范', 'education', '0', '0', 'admin', NOW(), '', NULL, '制度规范'),
(1006, 1004, '0,1004', '流程文档', 'PROCESS_DOC', 2, '业务流程文档', 'tree-table', '0', '0', 'admin', NOW(), '', NULL, '流程文档');

-- 2. 插入初始化标签数据
INSERT INTO `doc_tag` VALUES 
(1000, '重要', '#F56C6C', '重要文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1001, '紧急', '#E6A23C', '紧急文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1002, '草稿', '#909399', '草稿文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1003, '已发布', '#67C23A', '已发布文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1004, '待审核', '#409EFF', '待审核文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1005, '已归档', '#909399', '已归档文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1006, 'Java', '#FF6B35', 'Java技术标签', 0, '0', 'admin', NOW(), '', NULL),
(1007, 'Vue', '#4FC08D', 'Vue技术标签', 0, '0', 'admin', NOW(), '', NULL),
(1008, 'MySQL', '#00758F', 'MySQL技术标签', 0, '0', 'admin', NOW(), '', NULL),
(1009, 'Spring Boot', '#6DB33F', 'Spring Boot技术标签', 0, '0', 'admin', NOW(), '', NULL);
