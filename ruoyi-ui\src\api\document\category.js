import request from '@/utils/request'

// 查询文档分类列表
export function listCategory(query) {
  return request({
    url: '/document/category/list',
    method: 'get',
    params: query
  })
}

// 查询文档分类详细
export function getCategory(categoryId) {
  return request({
    url: '/document/category/' + categoryId,
    method: 'get'
  })
}

// 查询文档分类下拉树结构
export function treeselect() {
  return request({
    url: '/document/category/treeselect',
    method: 'get'
  })
}

// 根据角色ID查询分类树结构
export function roleTreeselect(roleId) {
  return request({
    url: '/document/category/roleTreeselect/' + roleId,
    method: 'get'
  })
}

// 新增文档分类
export function addCategory(data) {
  return request({
    url: '/document/category',
    method: 'post',
    data: data
  })
}

// 修改文档分类
export function updateCategory(data) {
  return request({
    url: '/document/category',
    method: 'put',
    data: data
  })
}

// 删除文档分类
export function delCategory(categoryId) {
  return request({
    url: '/document/category/' + categoryId,
    method: 'delete'
  })
}

// 校验分类名称
export function checkCategoryNameUnique(data) {
  return request({
    url: '/document/category/checkCategoryNameUnique',
    method: 'post',
    data: data
  })
}
