-- 文档管理系统数据库初始化脚本
-- 请在VS Code的Database扩展中执行此脚本

USE `ry-vue`;

-- 1. 创建文档分类表
DROP TABLE IF EXISTS `doc_category`;
CREATE TABLE `doc_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `ancestors` varchar(500) DEFAULT '' COMMENT '祖级列表',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) DEFAULT NULL COMMENT '分类编码',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `description` varchar(500) DEFAULT '' COMMENT '分类描述',
  `icon` varchar(100) DEFAULT '' COMMENT '分类图标',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档分类表';

-- 2. 创建文档标签表
DROP TABLE IF EXISTS `doc_tag`;
CREATE TABLE `doc_tag` (
  `tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_color` varchar(20) DEFAULT '#409EFF' COMMENT '标签颜色',
  `description` varchar(200) DEFAULT '' COMMENT '标签描述',
  `use_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`tag_id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档标签表';

-- 3. 创建文档主表
DROP TABLE IF EXISTS `doc_document`;
CREATE TABLE `doc_document` (
  `doc_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `doc_title` varchar(200) NOT NULL COMMENT '文档标题',
  `doc_code` varchar(100) DEFAULT NULL COMMENT '文档编号',
  `doc_type` varchar(20) NOT NULL COMMENT '文档类型',
  `file_name` varchar(200) NOT NULL COMMENT '文件名称',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `file_md5` varchar(32) DEFAULT NULL COMMENT '文件MD5值',
  `content` longtext COMMENT '文档内容',
  `summary` varchar(1000) DEFAULT '' COMMENT '文档摘要',
  `keywords` varchar(500) DEFAULT '' COMMENT '关键词',
  `author` varchar(100) DEFAULT '' COMMENT '文档作者',
  `version_num` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `current_version_id` bigint(20) DEFAULT NULL COMMENT '当前版本ID',
  `publish_status` char(1) DEFAULT '0' COMMENT '发布状态（0草稿 1发布 2归档）',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `is_public` char(1) DEFAULT '0' COMMENT '是否公开（0私有 1公开）',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`doc_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档主表';

-- 4. 创建文档标签关联表
DROP TABLE IF EXISTS `doc_document_tag`;
CREATE TABLE `doc_document_tag` (
  `doc_id` bigint(20) NOT NULL COMMENT '文档ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  PRIMARY KEY (`doc_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档标签关联表';
