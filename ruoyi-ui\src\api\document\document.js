import request from '@/utils/request'

// 查询文档列表
export function listDocument(query) {
  return request({
    url: '/document/doc/list',
    method: 'get',
    params: query
  })
}

// 查询文档详细
export function getDocument(docId) {
  return request({
    url: '/document/doc/' + docId,
    method: 'get'
  })
}

// 新增文档
export function addDocument(data) {
  return request({
    url: '/document/doc',
    method: 'post',
    data: data
  })
}

// 修改文档
export function updateDocument(data) {
  return request({
    url: '/document/doc',
    method: 'put',
    data: data
  })
}

// 删除文档
export function delDocument(docId) {
  return request({
    url: '/document/doc/' + docId,
    method: 'delete'
  })
}

// 上传文档
export function uploadDocument(data) {
  return request({
    url: '/document/doc/upload',
    method: 'post',
    data: data
  })
}

// 下载文档
export function downloadDocument(docId) {
  return request({
    url: '/document/doc/download/' + docId,
    method: 'get',
    responseType: 'blob'
  })
}

// 预览文档
export function previewDocument(docId) {
  return request({
    url: '/document/doc/preview/' + docId,
    method: 'get'
  })
}

// 全文搜索文档
export function searchDocuments(query) {
  return request({
    url: '/document/doc/search',
    method: 'get',
    params: query
  })
}

// 发布文档
export function publishDocument(docId) {
  return request({
    url: '/document/doc/publish/' + docId,
    method: 'put'
  })
}

// 归档文档
export function archiveDocument(docId) {
  return request({
    url: '/document/doc/archive/' + docId,
    method: 'put'
  })
}

// 获取文档版本历史
export function getDocumentVersions(docId) {
  return request({
    url: '/document/doc/versions/' + docId,
    method: 'get'
  })
}

// 版本回滚
export function rollbackVersion(docId, versionId) {
  return request({
    url: '/document/doc/rollback/' + docId + '/' + versionId,
    method: 'put'
  })
}

// 分享文档
export function shareDocument(docId, data) {
  return request({
    url: '/document/doc/share/' + docId,
    method: 'post',
    data: data
  })
}

// 收藏/取消收藏文档
export function toggleFavorite(docId) {
  return request({
    url: '/document/doc/favorite/' + docId,
    method: 'post'
  })
}

// 获取收藏的文档
export function getFavoriteDocuments(query) {
  return request({
    url: '/document/doc/favorites',
    method: 'get',
    params: query
  })
}

// 获取最近访问的文档
export function getRecentDocuments(query) {
  return request({
    url: '/document/doc/recent',
    method: 'get',
    params: query
  })
}

// 批量操作文档
export function batchOperation(data) {
  return request({
    url: '/document/doc/batch',
    method: 'post',
    data: data
  })
}
