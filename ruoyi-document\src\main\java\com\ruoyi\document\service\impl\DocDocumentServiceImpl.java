package com.ruoyi.document.service.impl;

import java.util.List;
import java.util.Date;
import java.io.File;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.document.domain.DocDocument;
import com.ruoyi.document.domain.DocVersion;
import com.ruoyi.document.mapper.DocDocumentMapper;
import com.ruoyi.document.service.IDocDocumentService;

/**
 * 文档管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class DocDocumentServiceImpl implements IDocDocumentService 
{
    @Autowired
    private DocDocumentMapper docDocumentMapper;

    /**
     * 查询文档
     * 
     * @param docId 文档主键
     * @return 文档
     */
    @Override
    public DocDocument selectDocDocumentByDocId(Long docId)
    {
        DocDocument document = docDocumentMapper.selectDocDocumentByDocId(docId);
        if (document != null) {
            // 更新查看次数
            docDocumentMapper.updateViewCount(docId);
            // 记录访问日志
            // TODO: 记录操作日志
        }
        return document;
    }

    /**
     * 查询文档列表
     * 
     * @param docDocument 文档
     * @return 文档
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<DocDocument> selectDocDocumentList(DocDocument docDocument)
    {
        return docDocumentMapper.selectDocDocumentList(docDocument);
    }

    /**
     * 新增文档
     * 
     * @param docDocument 文档
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDocDocument(DocDocument docDocument)
    {
        docDocument.setCreateBy(SecurityUtils.getUsername());
        docDocument.setCreateTime(DateUtils.getNowDate());
        
        // 生成文档编号
        if (StringUtils.isEmpty(docDocument.getDocCode())) {
            docDocument.setDocCode(generateDocumentCode(docDocument.getCategoryId()));
        }
        
        // 设置默认值
        if (docDocument.getViewCount() == null) {
            docDocument.setViewCount(0);
        }
        if (docDocument.getDownloadCount() == null) {
            docDocument.setDownloadCount(0);
        }
        if (StringUtils.isEmpty(docDocument.getPublishStatus())) {
            docDocument.setPublishStatus("0"); // 默认草稿状态
        }
        if (StringUtils.isEmpty(docDocument.getStatus())) {
            docDocument.setStatus("0"); // 默认正常状态
        }
        if (StringUtils.isEmpty(docDocument.getDelFlag())) {
            docDocument.setDelFlag("0"); // 默认未删除
        }
        
        int result = docDocumentMapper.insertDocDocument(docDocument);
        
        // 处理标签关联
        if (docDocument.getTagIds() != null && !docDocument.getTagIds().isEmpty()) {
            docDocumentMapper.batchInsertDocumentTags(docDocument.getDocId(), docDocument.getTagIds());
        }
        
        return result;
    }

    /**
     * 修改文档
     * 
     * @param docDocument 文档
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDocDocument(DocDocument docDocument)
    {
        docDocument.setUpdateBy(SecurityUtils.getUsername());
        docDocument.setUpdateTime(DateUtils.getNowDate());
        
        // 先删除原有标签关联
        docDocumentMapper.deleteDocumentTags(docDocument.getDocId());
        
        // 重新建立标签关联
        if (docDocument.getTagIds() != null && !docDocument.getTagIds().isEmpty()) {
            docDocumentMapper.batchInsertDocumentTags(docDocument.getDocId(), docDocument.getTagIds());
        }
        
        return docDocumentMapper.updateDocDocument(docDocument);
    }

    /**
     * 批量删除文档
     * 
     * @param docIds 需要删除的文档主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDocDocumentByDocIds(Long[] docIds)
    {
        // 删除标签关联
        for (Long docId : docIds) {
            docDocumentMapper.deleteDocumentTags(docId);
        }
        
        return docDocumentMapper.deleteDocDocumentByDocIds(docIds);
    }

    /**
     * 删除文档信息
     * 
     * @param docId 文档主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDocDocumentByDocId(Long docId)
    {
        // 删除标签关联
        docDocumentMapper.deleteDocumentTags(docId);
        
        return docDocumentMapper.deleteDocDocumentByDocId(docId);
    }

    /**
     * 上传文档
     * 
     * @param file 上传的文件
     * @param docDocument 文档信息
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult uploadDocument(MultipartFile file, DocDocument docDocument)
    {
        try {
            // 上传文件
            String fileName = FileUploadUtils.upload(RuoYiConfig.getUploadPath() + "/document", file);
            String filePath = RuoYiConfig.getUploadPath() + "/document" + fileName;
            
            // 设置文档信息
            docDocument.setFileName(file.getOriginalFilename());
            docDocument.setFilePath(fileName);
            docDocument.setFileSize(file.getSize());
            docDocument.setDocType(getFileExtension(file.getOriginalFilename()));
            
            // 计算文件MD5
            String fileMd5 = FileUtils.getMd5(new File(filePath));
            docDocument.setFileMd5(fileMd5);
            
            // 检查是否已存在相同文件
            DocDocument existDocument = docDocumentMapper.selectDocumentByMd5(fileMd5);
            if (existDocument != null) {
                return AjaxResult.warn("文件已存在：" + existDocument.getDocTitle());
            }
            
            // 提取文档内容
            String content = extractDocumentContent(filePath, docDocument.getDocType());
            docDocument.setContent(content);
            
            // 保存文档信息
            int result = insertDocDocument(docDocument);
            
            if (result > 0) {
                return AjaxResult.success("文档上传成功", docDocument);
            } else {
                return AjaxResult.error("文档上传失败");
            }
            
        } catch (Exception e) {
            return AjaxResult.error("文档上传失败：" + e.getMessage());
        }
    }

    /**
     * 下载文档
     * 
     * @param docId 文档ID
     * @param response HTTP响应
     */
    @Override
    public void downloadDocument(Long docId, HttpServletResponse response)
    {
        try {
            DocDocument document = docDocumentMapper.selectDocDocumentByDocId(docId);
            if (document != null) {
                String filePath = RuoYiConfig.getUploadPath() + "/document" + document.getFilePath();
                FileUtils.downloadFile(filePath, document.getFileName(), response);
                
                // 更新下载次数
                docDocumentMapper.updateDownloadCount(docId);
            }
        } catch (Exception e) {
            throw new RuntimeException("文档下载失败：" + e.getMessage());
        }
    }

    /**
     * 预览文档
     * 
     * @param docId 文档ID
     * @return 预览结果
     */
    @Override
    public AjaxResult previewDocument(Long docId)
    {
        try {
            DocDocument document = docDocumentMapper.selectDocDocumentByDocId(docId);
            if (document != null) {
                // 根据文档类型返回预览内容
                if ("txt".equals(document.getDocType()) || "md".equals(document.getDocType())) {
                    return AjaxResult.success("预览成功", document.getContent());
                } else {
                    // 对于其他类型，返回文档信息和下载链接
                    return AjaxResult.success("预览成功", document);
                }
            } else {
                return AjaxResult.error("文档不存在");
            }
        } catch (Exception e) {
            return AjaxResult.error("文档预览失败：" + e.getMessage());
        }
    }

    /**
     * 全文搜索文档
     * 
     * @param keyword 搜索关键词
     * @param docDocument 查询条件
     * @return 文档集合
     */
    @Override
    public List<DocDocument> searchDocuments(String keyword, DocDocument docDocument)
    {
        return docDocumentMapper.searchDocuments(keyword, docDocument);
    }

    /**
     * 发布文档
     * 
     * @param docId 文档ID
     * @return 结果
     */
    @Override
    public int publishDocument(Long docId)
    {
        DocDocument document = new DocDocument();
        document.setDocId(docId);
        document.setPublishStatus("1");
        document.setUpdateBy(SecurityUtils.getUsername());
        document.setUpdateTime(DateUtils.getNowDate());
        return docDocumentMapper.updateDocDocument(document);
    }

    /**
     * 归档文档
     * 
     * @param docId 文档ID
     * @return 结果
     */
    @Override
    public int archiveDocument(Long docId)
    {
        DocDocument document = new DocDocument();
        document.setDocId(docId);
        document.setPublishStatus("2");
        document.setUpdateBy(SecurityUtils.getUsername());
        document.setUpdateTime(DateUtils.getNowDate());
        return docDocumentMapper.updateDocDocument(document);
    }

    /**
     * 获取文档版本历史
     * 
     * @param docId 文档ID
     * @return 版本列表
     */
    @Override
    public List<DocVersion> getDocumentVersions(Long docId)
    {
        // TODO: 实现版本历史查询
        return null;
    }

    /**
     * 版本回滚
     * 
     * @param docId 文档ID
     * @param versionId 版本ID
     * @return 结果
     */
    @Override
    public int rollbackVersion(Long docId, Long versionId)
    {
        // TODO: 实现版本回滚
        return 0;
    }

    /**
     * 分享文档
     * 
     * @param docId 文档ID
     * @param shareConfig 分享配置
     * @return 分享结果
     */
    @Override
    public AjaxResult shareDocument(Long docId, String shareConfig)
    {
        // TODO: 实现文档分享
        return AjaxResult.success("分享成功");
    }

    /**
     * 收藏/取消收藏文档
     * 
     * @param docId 文档ID
     * @return 结果
     */
    @Override
    public int toggleFavorite(Long docId)
    {
        // TODO: 实现收藏功能
        return 1;
    }

    /**
     * 获取收藏的文档
     * 
     * @param docDocument 查询条件
     * @return 文档集合
     */
    @Override
    public List<DocDocument> getFavoriteDocuments(DocDocument docDocument)
    {
        Long userId = SecurityUtils.getUserId();
        return docDocumentMapper.selectFavoriteDocuments(userId, docDocument);
    }

    /**
     * 获取最近访问的文档
     * 
     * @param docDocument 查询条件
     * @return 文档集合
     */
    @Override
    public List<DocDocument> getRecentDocuments(DocDocument docDocument)
    {
        Long userId = SecurityUtils.getUserId();
        return docDocumentMapper.selectRecentDocuments(userId, docDocument);
    }

    /**
     * 批量操作文档
     * 
     * @param operation 操作配置
     * @return 操作结果
     */
    @Override
    public AjaxResult batchOperation(String operation)
    {
        // TODO: 实现批量操作
        return AjaxResult.success("批量操作成功");
    }

    /**
     * 提取文档内容
     * 
     * @param filePath 文件路径
     * @param fileType 文件类型
     * @return 文档内容
     */
    @Override
    public String extractDocumentContent(String filePath, String fileType)
    {
        // TODO: 实现文档内容提取
        return "";
    }

    /**
     * 生成文档编号
     * 
     * @param categoryId 分类ID
     * @return 文档编号
     */
    @Override
    public String generateDocumentCode(Long categoryId)
    {
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        return "DOC" + categoryId + dateStr + System.currentTimeMillis() % 10000;
    }

    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名
     */
    private String getFileExtension(String fileName)
    {
        if (StringUtils.isNotEmpty(fileName) && fileName.contains(".")) {
            return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        }
        return "";
    }
}
