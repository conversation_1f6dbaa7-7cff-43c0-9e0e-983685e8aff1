<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文档标题" prop="docTitle">
        <el-input
          v-model="queryParams.docTitle"
          placeholder="请输入文档标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-cascader
          v-model="queryParams.categoryId"
          :options="categoryOptions"
          :props="{ checkStrictly: true, value: 'categoryId', label: 'categoryName', children: 'children' }"
          placeholder="请选择文档分类"
          clearable
        />
      </el-form-item>
      <el-form-item label="文档类型" prop="docType">
        <el-select v-model="queryParams.docType" placeholder="请选择文档类型" clearable>
          <el-option label="Word文档" value="doc,docx" />
          <el-option label="PDF文档" value="pdf" />
          <el-option label="Excel表格" value="xls,xlsx" />
          <el-option label="PPT演示" value="ppt,pptx" />
          <el-option label="文本文档" value="txt,md" />
          <el-option label="压缩文件" value="zip,rar" />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态" prop="publishStatus">
        <el-select v-model="queryParams.publishStatus" placeholder="请选择发布状态" clearable>
          <el-option label="草稿" value="0" />
          <el-option label="已发布" value="1" />
          <el-option label="已归档" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 全文搜索 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="12">
        <el-input
          v-model="searchKeyword"
          placeholder="输入关键词进行全文搜索..."
          class="search-input"
          @keyup.enter.native="handleFullTextSearch"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleFullTextSearch"></el-button>
        </el-input>
      </el-col>
      <el-col :span="12">
        <div class="search-tags">
          <el-tag
            v-for="tag in searchTags"
            :key="tag.tagId"
            :type="tag.selected ? 'primary' : 'info'"
            @click="toggleSearchTag(tag)"
            style="margin-right: 8px; cursor: pointer;"
          >
            {{ tag.tagName }}
          </el-tag>
        </div>
      </el-col>
    </el-row>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['document:list:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleUpload"
          v-hasPermi="['document:upload:view']"
        >上传</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['document:list:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['document:list:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 视图切换 -->
    <el-row class="mb8">
      <el-radio-group v-model="viewMode" size="mini">
        <el-radio-button label="list">列表视图</el-radio-button>
        <el-radio-button label="card">卡片视图</el-radio-button>
        <el-radio-button label="tree">分类树视图</el-radio-button>
      </el-radio-group>
    </el-row>

    <!-- 列表视图 -->
    <el-table 
      v-if="viewMode === 'list'"
      v-loading="loading" 
      :data="docList" 
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档标题" align="center" prop="docTitle" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-link @click="handlePreview(scope.row)" :underline="false" type="primary">
            <i :class="getFileIcon(scope.row.docType)" style="margin-right: 5px;"></i>
            {{ scope.row.docTitle }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" prop="categoryName" />
      <el-table-column label="文档类型" align="center" prop="docType">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getTypeTagType(scope.row.docType)">{{ scope.row.docType.toUpperCase() }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="文件大小" align="center" prop="fileSize">
        <template slot-scope="scope">
          {{ formatFileSize(scope.row.fileSize) }}
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" prop="tags" width="200">
        <template slot-scope="scope">
          <el-tag
            v-for="tag in scope.row.tags"
            :key="tag.tagId"
            size="mini"
            :color="tag.tagColor"
            style="margin-right: 4px;"
          >
            {{ tag.tagName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" prop="publishStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.doc_publish_status" :value="scope.row.publishStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="查看次数" align="center" prop="viewCount" />
      <el-table-column label="下载次数" align="center" prop="downloadCount" />
      <el-table-column label="创建者" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handlePreview(scope.row)"
            v-hasPermi="['document:list:query']"
          >预览</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDownload(scope.row)"
            v-hasPermi="['document:list:download']"
          >下载</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['document:list:edit']"
          >修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['document:list:edit']">
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="versions" icon="el-icon-time">版本历史</el-dropdown-item>
              <el-dropdown-item command="share" icon="el-icon-share">分享</el-dropdown-item>
              <el-dropdown-item command="favorite" icon="el-icon-star-off">收藏</el-dropdown-item>
              <el-dropdown-item command="publish" icon="el-icon-upload2" v-if="scope.row.publishStatus === '0'">发布</el-dropdown-item>
              <el-dropdown-item command="archive" icon="el-icon-box" v-if="scope.row.publishStatus === '1'">归档</el-dropdown-item>
              <el-dropdown-item command="delete" icon="el-icon-delete" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 卡片视图 -->
    <div v-if="viewMode === 'card'" class="card-container">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="doc in docList" :key="doc.docId">
          <el-card class="doc-card" shadow="hover" @click.native="handlePreview(doc)">
            <div class="card-header">
              <i :class="getFileIcon(doc.docType)" class="file-icon"></i>
              <div class="card-title">{{ doc.docTitle }}</div>
            </div>
            <div class="card-content">
              <p class="doc-summary">{{ doc.summary || '暂无摘要' }}</p>
              <div class="doc-tags">
                <el-tag
                  v-for="tag in doc.tags"
                  :key="tag.tagId"
                  size="mini"
                  :color="tag.tagColor"
                  style="margin-right: 4px;"
                >
                  {{ tag.tagName }}
                </el-tag>
              </div>
            </div>
            <div class="card-footer">
              <span class="doc-info">{{ formatFileSize(doc.fileSize) }}</span>
              <span class="doc-info">{{ parseTime(doc.createTime, '{y}-{m}-{d}') }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listDocument, getDocument, delDocument, downloadDocument, previewDocument, searchDocuments, publishDocument, archiveDocument, toggleFavorite, getFavoriteDocuments, getRecentDocuments } from "@/api/document/document";
import { treeselect } from "@/api/document/category";

export default {
  name: "DocumentList",
  dicts: ['doc_publish_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文档表格数据
      docList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docTitle: null,
        categoryId: null,
        docType: null,
        publishStatus: null,
        author: null,
        createBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 分类选项
      categoryOptions: [],
      // 日期范围
      dateRange: [],
      // 搜索关键词
      searchKeyword: '',
      // 搜索标签
      searchTags: [],
      // 视图模式
      viewMode: 'list'
    };
  },
  created() {
    this.getList();
    this.getCategoryTree();
  },
  methods: {
    /** 查询文档列表 */
    getList() {
      this.loading = true;
      listDocument(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.docList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询分类下拉树结构 */
    getCategoryTree() {
      treeselect().then(response => {
        this.categoryOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        docId: null,
        categoryId: null,
        docTitle: null,
        docCode: null,
        docType: null,
        fileName: null,
        filePath: null,
        fileSize: null,
        fileMd5: null,
        content: null,
        summary: null,
        keywords: null,
        author: null,
        versionNum: "1.0",
        currentVersionId: null,
        publishStatus: "0",
        viewCount: 0,
        downloadCount: 0,
        isPublic: "0",
        expireTime: null,
        status: "0",
        delFlag: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加文档";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const docId = row.docId || this.ids
      getDocument(docId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改文档";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.docId != null) {
            updateDocument(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDocument(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const docIds = row.docId || this.ids;
      this.$modal.confirm('是否确认删除文档编号为"' + docIds + '"的数据项？').then(function() {
        return delDocument(docIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 上传按钮操作 */
    handleUpload() {
      this.$router.push('/document/upload');
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('document/doc/export', {
        ...this.queryParams
      }, `document_${new Date().getTime()}.xlsx`)
    },
    /** 预览文档 */
    handlePreview(row) {
      previewDocument(row.docId).then(response => {
        // 根据文档类型处理预览
        if (row.docType === 'txt' || row.docType === 'md') {
          this.$alert(response.data, '文档预览', {
            dangerouslyUseHTMLString: true
          });
        } else {
          // 其他类型文档，提供下载
          this.handleDownload(row);
        }
      });
    },
    /** 下载文档 */
    handleDownload(row) {
      downloadDocument(row.docId).then(response => {
        this.download(response, row.fileName);
      });
    },
    /** 全文搜索 */
    handleFullTextSearch() {
      if (this.searchKeyword) {
        this.queryParams.pageNum = 1;
        searchDocuments({
          keyword: this.searchKeyword,
          ...this.queryParams
        }).then(response => {
          this.docList = response.rows;
          this.total = response.total;
        });
      } else {
        this.getList();
      }
    },
    /** 切换搜索标签 */
    toggleSearchTag(tag) {
      tag.selected = !tag.selected;
      // TODO: 根据选中的标签过滤文档
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case 'versions':
          this.showVersionHistory(row);
          break;
        case 'share':
          this.shareDocument(row);
          break;
        case 'favorite':
          this.toggleDocumentFavorite(row);
          break;
        case 'publish':
          this.publishDocument(row);
          break;
        case 'archive':
          this.archiveDocument(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },
    /** 显示版本历史 */
    showVersionHistory(row) {
      this.$message.info('版本历史功能开发中...');
    },
    /** 分享文档 */
    shareDocument(row) {
      this.$message.info('文档分享功能开发中...');
    },
    /** 收藏文档 */
    toggleDocumentFavorite(row) {
      toggleFavorite(row.docId).then(response => {
        this.$modal.msgSuccess("操作成功");
      });
    },
    /** 发布文档 */
    publishDocument(row) {
      this.$modal.confirm('确认发布文档"' + row.docTitle + '"？').then(function() {
        return publishDocument(row.docId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("发布成功");
      }).catch(() => {});
    },
    /** 归档文档 */
    archiveDocument(row) {
      this.$modal.confirm('确认归档文档"' + row.docTitle + '"？').then(function() {
        return archiveDocument(row.docId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("归档成功");
      }).catch(() => {});
    },
    /** 获取文件图标 */
    getFileIcon(docType) {
      const iconMap = {
        'pdf': 'el-icon-document',
        'doc': 'el-icon-document',
        'docx': 'el-icon-document',
        'xls': 'el-icon-s-grid',
        'xlsx': 'el-icon-s-grid',
        'ppt': 'el-icon-present',
        'pptx': 'el-icon-present',
        'txt': 'el-icon-document-copy',
        'md': 'el-icon-document-copy',
        'zip': 'el-icon-folder-opened',
        'rar': 'el-icon-folder-opened'
      };
      return iconMap[docType] || 'el-icon-document';
    },
    /** 获取类型标签样式 */
    getTypeTagType(docType) {
      const typeMap = {
        'pdf': 'danger',
        'doc': 'primary',
        'docx': 'primary',
        'xls': 'success',
        'xlsx': 'success',
        'ppt': 'warning',
        'pptx': 'warning',
        'txt': 'info',
        'md': 'info'
      };
      return typeMap[docType] || '';
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + ' KB';
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(1) + ' MB';
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
      }
    }
  }
};
</script>

<style scoped>
.search-input {
  margin-bottom: 10px;
}

.search-tags {
  text-align: right;
  padding-top: 5px;
}

.card-container {
  margin-top: 20px;
}

.doc-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.doc-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.file-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.card-title {
  font-weight: bold;
  font-size: 16px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-content {
  margin-bottom: 15px;
}

.doc-summary {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.doc-tags {
  margin-bottom: 10px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  color: #999;
  font-size: 12px;
}

.doc-info {
  margin-right: 10px;
}
</style>
