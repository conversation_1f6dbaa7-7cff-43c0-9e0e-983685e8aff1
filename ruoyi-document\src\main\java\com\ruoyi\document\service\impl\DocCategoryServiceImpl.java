package com.ruoyi.document.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.document.domain.DocCategory;
import com.ruoyi.document.mapper.DocCategoryMapper;
import com.ruoyi.document.mapper.DocDocumentMapper;
import com.ruoyi.document.service.IDocCategoryService;

/**
 * 文档分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class DocCategoryServiceImpl implements IDocCategoryService 
{
    @Autowired
    private DocCategoryMapper docCategoryMapper;
    
    @Autowired
    private DocDocumentMapper docDocumentMapper;

    /**
     * 查询文档分类
     * 
     * @param categoryId 文档分类主键
     * @return 文档分类
     */
    @Override
    public DocCategory selectDocCategoryByCategoryId(Long categoryId)
    {
        return docCategoryMapper.selectDocCategoryByCategoryId(categoryId);
    }

    /**
     * 查询文档分类列表
     * 
     * @param docCategory 文档分类
     * @return 文档分类
     */
    @Override
    public List<DocCategory> selectDocCategoryList(DocCategory docCategory)
    {
        return docCategoryMapper.selectDocCategoryList(docCategory);
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param categories 分类列表
     * @return 树结构列表
     */
    @Override
    public List<DocCategory> buildCategoryTree(List<DocCategory> categories)
    {
        List<DocCategory> returnList = new ArrayList<DocCategory>();
        List<Long> tempList = new ArrayList<Long>();
        for (DocCategory category : categories)
        {
            tempList.add(category.getCategoryId());
        }
        for (Iterator<DocCategory> iterator = categories.iterator(); iterator.hasNext();)
        {
            DocCategory category = (DocCategory) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(category.getParentId()))
            {
                recursionFn(categories, category);
                returnList.add(category);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = categories;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param categories 分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<Ztree> initZtree(List<DocCategory> categories)
    {
        return initZtree(categories, null, false);
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param categories 分类列表
     * @param roleMenus 角色已存在菜单列表
     * @param permsFlag 是否需要显示权限标识
     * @return 下拉树结构列表
     */
    public List<Ztree> initZtree(List<DocCategory> categories, List<String> roleMenus, boolean permsFlag)
    {
        List<Ztree> ztrees = new ArrayList<Ztree>();
        boolean isCheck = StringUtils.isNotNull(roleMenus);
        for (DocCategory category : categories)
        {
            Ztree ztree = new Ztree();
            ztree.setId(category.getCategoryId());
            ztree.setpId(category.getParentId());
            ztree.setName(category.getCategoryName());
            ztree.setTitle(category.getCategoryName());
            if (isCheck)
            {
                ztree.setChecked(roleMenus.contains(category.getCategoryId() + category.getDescription()));
            }
            ztrees.add(ztree);
        }
        return ztrees;
    }

    /**
     * 新增文档分类
     * 
     * @param docCategory 文档分类
     * @return 结果
     */
    @Override
    public int insertDocCategory(DocCategory docCategory)
    {
        DocCategory info = docCategoryMapper.selectDocCategoryByCategoryId(docCategory.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!DocCategory.NORMAL.equals(info.getStatus()))
        {
            throw new RuntimeException("分类停用，不允许新增");
        }
        docCategory.setAncestors(info.getAncestors() + "," + docCategory.getParentId());
        docCategory.setCreateBy(SecurityUtils.getUsername());
        docCategory.setCreateTime(DateUtils.getNowDate());
        return docCategoryMapper.insertDocCategory(docCategory);
    }

    /**
     * 修改文档分类
     * 
     * @param docCategory 文档分类
     * @return 结果
     */
    @Override
    public int updateDocCategory(DocCategory docCategory)
    {
        DocCategory newParentCategory = docCategoryMapper.selectDocCategoryByCategoryId(docCategory.getParentId());
        DocCategory oldCategory = docCategoryMapper.selectDocCategoryByCategoryId(docCategory.getCategoryId());
        if (StringUtils.isNotNull(newParentCategory) && StringUtils.isNotNull(oldCategory))
        {
            String newAncestors = newParentCategory.getAncestors() + "," + newParentCategory.getCategoryId();
            String oldAncestors = oldCategory.getAncestors();
            docCategory.setAncestors(newAncestors);
            updateCategoryChildren(docCategory.getCategoryId(), newAncestors, oldAncestors);
        }
        docCategory.setUpdateBy(SecurityUtils.getUsername());
        docCategory.setUpdateTime(DateUtils.getNowDate());
        return docCategoryMapper.updateDocCategory(docCategory);
    }

    /**
     * 修改子元素关系
     * 
     * @param categoryId 被修改的分类ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateCategoryChildren(Long categoryId, String newAncestors, String oldAncestors)
    {
        List<DocCategory> children = docCategoryMapper.selectChildrenCategoryById(categoryId);
        for (DocCategory child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            docCategoryMapper.updateCategoryChildren(children);
        }
    }

    /**
     * 批量删除文档分类
     * 
     * @param categoryIds 需要删除的文档分类主键
     * @return 结果
     */
    @Override
    public int deleteDocCategoryByCategoryIds(Long[] categoryIds)
    {
        for (Long categoryId : categoryIds)
        {
            checkCategoryAllowed(categoryId);
            if (hasChildByCategoryId(categoryId))
            {
                throw new RuntimeException("存在子分类,不允许删除");
            }
            if (checkCategoryExistDocument(categoryId))
            {
                throw new RuntimeException("分类存在文档,不允许删除");
            }
        }
        return docCategoryMapper.deleteDocCategoryByCategoryIds(categoryIds);
    }

    /**
     * 删除文档分类信息
     * 
     * @param categoryId 文档分类主键
     * @return 结果
     */
    @Override
    public int deleteDocCategoryByCategoryId(Long categoryId)
    {
        checkCategoryAllowed(categoryId);
        if (hasChildByCategoryId(categoryId))
        {
            throw new RuntimeException("存在子分类,不允许删除");
        }
        if (checkCategoryExistDocument(categoryId))
        {
            throw new RuntimeException("分类存在文档,不允许删除");
        }
        return docCategoryMapper.deleteDocCategoryByCategoryId(categoryId);
    }

    /**
     * 检查分类名称是否唯一
     * 
     * @param docCategory 分类信息
     * @return 结果
     */
    @Override
    public String checkCategoryNameUnique(DocCategory docCategory)
    {
        Long categoryId = StringUtils.isNull(docCategory.getCategoryId()) ? -1L : docCategory.getCategoryId();
        DocCategory info = docCategoryMapper.checkCategoryNameUnique(docCategory);
        if (StringUtils.isNotNull(info) && info.getCategoryId().longValue() != categoryId.longValue())
        {
            return "1";
        }
        return "0";
    }

    /**
     * 查询分类是否存在子分类
     * 
     * @param categoryId 分类ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean hasChildByCategoryId(Long categoryId)
    {
        int result = docCategoryMapper.selectChildrenCountById(categoryId);
        return result > 0;
    }

    /**
     * 查询分类下是否存在文档
     * 
     * @param categoryId 分类ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkCategoryExistDocument(Long categoryId)
    {
        int result = docDocumentMapper.countDocumentsByCategoryId(categoryId);
        return result > 0;
    }

    /**
     * 根据角色ID查询分类权限
     * 
     * @param roleId 角色ID
     * @return 分类ID列表
     */
    @Override
    public List<Long> selectCategoryListByRoleId(Long roleId)
    {
        return docCategoryMapper.selectCategoryListByRoleId(roleId);
    }

    /**
     * 根据用户ID查询分类权限
     * 
     * @param userId 用户ID
     * @return 分类ID列表
     */
    @Override
    public List<Long> selectCategoryListByUserId(Long userId)
    {
        return docCategoryMapper.selectCategoryListByUserId(userId);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<DocCategory> list, DocCategory t)
    {
        // 得到子节点列表
        List<DocCategory> childList = getChildList(list, t);
        t.setChildren(childList);
        for (DocCategory tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<DocCategory> getChildList(List<DocCategory> list, DocCategory t)
    {
        List<DocCategory> tlist = new ArrayList<DocCategory>();
        Iterator<DocCategory> it = list.iterator();
        while (it.hasNext())
        {
            DocCategory n = (DocCategory) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getCategoryId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DocCategory> list, DocCategory t)
    {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 校验分类是否允许操作
     * 
     * @param categoryId 分类ID
     */
    public void checkCategoryAllowed(Long categoryId)
    {
        if (categoryId != null && categoryId.equals(1L))
        {
            throw new RuntimeException("不允许操作系统分类");
        }
    }
}
