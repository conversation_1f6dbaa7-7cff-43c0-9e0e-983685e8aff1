<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.document.mapper.DocTagMapper">
    
    <resultMap type="DocTag" id="DocTagResult">
        <result property="tagId"    column="tag_id"    />
        <result property="tagName"    column="tag_name"    />
        <result property="tagColor"    column="tag_color"    />
        <result property="description"    column="description"    />
        <result property="useCount"    column="use_count"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDocTagVo">
        select tag_id, tag_name, tag_color, description, use_count, status, create_by, create_time, update_by, update_time from doc_tag
    </sql>

    <select id="selectDocTagList" parameterType="DocTag" resultMap="DocTagResult">
        <include refid="selectDocTagVo"/>
        <where>  
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
            <if test="tagColor != null  and tagColor != ''"> and tag_color = #{tagColor}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by use_count desc, create_time desc
    </select>
    
    <select id="selectDocTagByTagId" parameterType="Long" resultMap="DocTagResult">
        <include refid="selectDocTagVo"/>
        where tag_id = #{tagId}
    </select>
        
    <insert id="insertDocTag" parameterType="DocTag" useGeneratedKeys="true" keyProperty="tagId">
        insert into doc_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name,</if>
            <if test="tagColor != null">tag_color,</if>
            <if test="description != null">description,</if>
            <if test="useCount != null">use_count,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">#{tagName},</if>
            <if test="tagColor != null">#{tagColor},</if>
            <if test="description != null">#{description},</if>
            <if test="useCount != null">#{useCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDocTag" parameterType="DocTag">
        update doc_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name = #{tagName},</if>
            <if test="tagColor != null">tag_color = #{tagColor},</if>
            <if test="description != null">description = #{description},</if>
            <if test="useCount != null">use_count = #{useCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where tag_id = #{tagId}
    </update>

    <delete id="deleteDocTagByTagId" parameterType="Long">
        delete from doc_tag where tag_id = #{tagId}
    </delete>

    <delete id="deleteDocTagByTagIds" parameterType="String">
        delete from doc_tag where tag_id in 
        <foreach item="tagId" collection="array" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </delete>

    <select id="selectTagsByDocId" parameterType="Long" resultMap="DocTagResult">
        <include refid="selectDocTagVo"/> t
        inner join doc_document_tag dt on t.tag_id = dt.tag_id
        where dt.doc_id = #{docId} and t.status = '0'
        order by t.tag_name
    </select>

    <select id="checkTagNameUnique" parameterType="DocTag" resultMap="DocTagResult">
        <include refid="selectDocTagVo"/>
        where tag_name = #{tagName}
        <if test="tagId != null">
            and tag_id != #{tagId}
        </if>
        limit 1
    </select>

    <update id="updateTagUseCount">
        update doc_tag set use_count = use_count + #{increment} where tag_id = #{tagId}
    </update>

    <select id="selectHotTags" resultMap="DocTagResult">
        <include refid="selectDocTagVo"/>
        where status = '0'
        order by use_count desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

</mapper>
