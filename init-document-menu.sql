-- 文档管理系统菜单配置脚本
-- 请在VS Code的Database扩展中执行此脚本

USE `ry-vue`;

-- 删除可能存在的文档管理菜单
DELETE FROM sys_role_menu WHERE menu_id >= 3000 AND menu_id <= 3020;
DELETE FROM sys_menu WHERE menu_id >= 3000 AND menu_id <= 3020;

-- 一级菜单：文档管理
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3000, '文档管理', 0, 6, 'document', NULL, '', 1, 0, 'M', '0', '0', '', 'documentation', 'admin', NOW(), '文档管理目录');

-- 二级菜单：文档列表
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3001, '文档列表', 3000, 1, 'list', 'document/list/index', '', 1, 0, 'C', '0', '0', 'document:list:view', 'list', 'admin', NOW(), '文档列表菜单');

-- 二级菜单：文档上传
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3002, '文档上传', 3000, 2, 'upload', 'document/upload/index', '', 1, 0, 'C', '0', '0', 'document:upload:view', 'upload', 'admin', NOW(), '文档上传菜单');

-- 二级菜单：分类管理
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3003, '分类管理', 3000, 3, 'category', 'document/category/index', '', 1, 0, 'C', '0', '0', 'document:category:view', 'tree', 'admin', NOW(), '分类管理菜单');

-- 二级菜单：标签管理
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3004, '标签管理', 3000, 4, 'tag', 'document/tag/index', '', 1, 0, 'C', '0', '0', 'document:tag:view', 'price-tag', 'admin', NOW(), '标签管理菜单');

-- 文档操作按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3010, '文档查询', 3001, 1, '', '', '', 1, 0, 'F', '0', '0', 'document:list:query', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3011, '文档新增', 3001, 2, '', '', '', 1, 0, 'F', '0', '0', 'document:list:add', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3012, '文档修改', 3001, 3, '', '', '', 1, 0, 'F', '0', '0', 'document:list:edit', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3013, '文档删除', 3001, 4, '', '', '', 1, 0, 'F', '0', '0', 'document:list:remove', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3014, '文档下载', 3001, 5, '', '', '', 1, 0, 'F', '0', '0', 'document:list:download', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3015, '文档分享', 3001, 6, '', '', '', 1, 0, 'F', '0', '0', 'document:list:share', '#', 'admin', NOW(), '');

-- 分类管理按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3016, '分类新增', 3003, 1, '', '', '', 1, 0, 'F', '0', '0', 'document:category:add', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3017, '分类修改', 3003, 2, '', '', '', 1, 0, 'F', '0', '0', 'document:category:edit', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3018, '分类删除', 3003, 3, '', '', '', 1, 0, 'F', '0', '0', 'document:category:remove', '#', 'admin', NOW(), '');

-- 为超级管理员角色分配文档管理权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_id >= 3000 AND menu_id <= 3020;

-- 添加文档配置参数
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
VALUES 
('文档上传路径', 'document.upload.path', '/profile/document', 'Y', 'admin', NOW(), '文档上传保存路径'),
('文档最大上传大小', 'document.upload.maxSize', '100', 'Y', 'admin', NOW(), '文档最大上传大小（MB）'),
('允许上传的文档类型', 'document.upload.allowTypes', 'doc,docx,pdf,xls,xlsx,ppt,pptx,txt,md,zip,rar', 'Y', 'admin', NOW(), '允许上传的文档文件类型');
