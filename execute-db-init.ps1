# PowerShell脚本：执行数据库初始化
param(
    [string]$Host = "localhost",
    [int]$Port = 3308,
    [string]$Username = "root",
    [string]$Password = "ankaixin.docker.mysql",
    [string]$Database = "ry-vue"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "RuoYi-Vue 文档管理系统数据库初始化" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 检查MySQL.Data.dll是否可用
try {
    Add-Type -AssemblyName "MySql.Data"
    Write-Host "✅ MySQL.Data 程序集加载成功" -ForegroundColor Green
} catch {
    Write-Host "❌ MySQL.Data 程序集加载失败，尝试其他方法..." -ForegroundColor Yellow
}

# 构建连接字符串
$connectionString = "Server=$Host;Port=$Port;Database=$Database;Uid=$Username;Pwd=$Password;CharSet=utf8mb4;"

Write-Host "正在连接数据库..." -ForegroundColor Yellow
Write-Host "主机: $Host:$Port" -ForegroundColor Cyan
Write-Host "数据库: $Database" -ForegroundColor Cyan
Write-Host "用户: $Username" -ForegroundColor Cyan

# SQL初始化脚本
$sqlScript = @"
USE ``ry-vue``;

-- 创建文档分类表
DROP TABLE IF EXISTS ``doc_category``;
CREATE TABLE ``doc_category`` (
  ``category_id`` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  ``parent_id`` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  ``ancestors`` varchar(500) DEFAULT '' COMMENT '祖级列表',
  ``category_name`` varchar(100) NOT NULL COMMENT '分类名称',
  ``category_code`` varchar(50) DEFAULT NULL COMMENT '分类编码',
  ``order_num`` int(4) DEFAULT 0 COMMENT '显示顺序',
  ``description`` varchar(500) DEFAULT '' COMMENT '分类描述',
  ``icon`` varchar(100) DEFAULT '' COMMENT '分类图标',
  ``status`` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  ``del_flag`` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  ``create_by`` varchar(64) DEFAULT '' COMMENT '创建者',
  ``create_time`` datetime DEFAULT NULL COMMENT '创建时间',
  ``update_by`` varchar(64) DEFAULT '' COMMENT '更新者',
  ``update_time`` datetime DEFAULT NULL COMMENT '更新时间',
  ``remark`` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (``category_id``),
  KEY ``idx_parent_id`` (``parent_id``),
  KEY ``idx_category_code`` (``category_code``),
  KEY ``idx_status`` (``status``)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档分类表';

-- 创建文档标签表
DROP TABLE IF EXISTS ``doc_tag``;
CREATE TABLE ``doc_tag`` (
  ``tag_id`` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  ``tag_name`` varchar(50) NOT NULL COMMENT '标签名称',
  ``tag_color`` varchar(20) DEFAULT '#409EFF' COMMENT '标签颜色',
  ``description`` varchar(200) DEFAULT '' COMMENT '标签描述',
  ``use_count`` int(11) DEFAULT 0 COMMENT '使用次数',
  ``status`` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  ``create_by`` varchar(64) DEFAULT '' COMMENT '创建者',
  ``create_time`` datetime DEFAULT NULL COMMENT '创建时间',
  ``update_by`` varchar(64) DEFAULT '' COMMENT '更新者',
  ``update_time`` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (``tag_id``),
  UNIQUE KEY ``uk_tag_name`` (``tag_name``),
  KEY ``idx_status`` (``status``)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档标签表';

-- 创建文档主表
DROP TABLE IF EXISTS ``doc_document``;
CREATE TABLE ``doc_document`` (
  ``doc_id`` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  ``category_id`` bigint(20) NOT NULL COMMENT '分类ID',
  ``doc_title`` varchar(200) NOT NULL COMMENT '文档标题',
  ``doc_code`` varchar(100) DEFAULT NULL COMMENT '文档编号',
  ``doc_type`` varchar(20) NOT NULL COMMENT '文档类型',
  ``file_name`` varchar(200) NOT NULL COMMENT '文件名称',
  ``file_path`` varchar(500) NOT NULL COMMENT '文件路径',
  ``file_size`` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  ``file_md5`` varchar(32) DEFAULT NULL COMMENT '文件MD5值',
  ``content`` longtext COMMENT '文档内容',
  ``summary`` varchar(1000) DEFAULT '' COMMENT '文档摘要',
  ``keywords`` varchar(500) DEFAULT '' COMMENT '关键词',
  ``author`` varchar(100) DEFAULT '' COMMENT '文档作者',
  ``version_num`` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  ``current_version_id`` bigint(20) DEFAULT NULL COMMENT '当前版本ID',
  ``publish_status`` char(1) DEFAULT '0' COMMENT '发布状态（0草稿 1发布 2归档）',
  ``view_count`` int(11) DEFAULT 0 COMMENT '查看次数',
  ``download_count`` int(11) DEFAULT 0 COMMENT '下载次数',
  ``is_public`` char(1) DEFAULT '0' COMMENT '是否公开（0私有 1公开）',
  ``expire_time`` datetime DEFAULT NULL COMMENT '过期时间',
  ``status`` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  ``del_flag`` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  ``create_by`` varchar(64) DEFAULT '' COMMENT '创建者',
  ``create_time`` datetime DEFAULT NULL COMMENT '创建时间',
  ``update_by`` varchar(64) DEFAULT '' COMMENT '更新者',
  ``update_time`` datetime DEFAULT NULL COMMENT '更新时间',
  ``remark`` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (``doc_id``),
  KEY ``idx_category_id`` (``category_id``),
  KEY ``idx_doc_code`` (``doc_code``),
  KEY ``idx_doc_type`` (``doc_type``),
  KEY ``idx_publish_status`` (``publish_status``),
  KEY ``idx_create_by`` (``create_by``),
  KEY ``idx_create_time`` (``create_time``),
  KEY ``idx_file_md5`` (``file_md5``)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档主表';

-- 创建文档标签关联表
DROP TABLE IF EXISTS ``doc_document_tag``;
CREATE TABLE ``doc_document_tag`` (
  ``doc_id`` bigint(20) NOT NULL COMMENT '文档ID',
  ``tag_id`` bigint(20) NOT NULL COMMENT '标签ID',
  PRIMARY KEY (``doc_id``,``tag_id``),
  KEY ``idx_tag_id`` (``tag_id``)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档标签关联表';
"@

# 分割SQL脚本为多个命令
$sqlCommands = $sqlScript -split ";"

Write-Host "开始执行数据库初始化..." -ForegroundColor Yellow

try {
    # 尝试使用MySQL.Data连接
    $connection = New-Object MySql.Data.MySqlClient.MySqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "✅ 数据库连接成功" -ForegroundColor Green
    
    $successCount = 0
    $totalCount = 0
    
    foreach ($sqlCommand in $sqlCommands) {
        $sqlCommand = $sqlCommand.Trim()
        if ($sqlCommand -ne "" -and -not $sqlCommand.StartsWith("--")) {
            $totalCount++
            try {
                $command = New-Object MySql.Data.MySqlClient.MySqlCommand($sqlCommand, $connection)
                $result = $command.ExecuteNonQuery()
                $successCount++
                Write-Host "✅ 执行成功: $($sqlCommand.Substring(0, [Math]::Min(50, $sqlCommand.Length)))..." -ForegroundColor Green
            } catch {
                Write-Host "❌ 执行失败: $($sqlCommand.Substring(0, [Math]::Min(50, $sqlCommand.Length)))..." -ForegroundColor Red
                Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    
    $connection.Close()
    
    Write-Host "" -ForegroundColor White
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "数据库初始化完成！" -ForegroundColor Green
    Write-Host "成功执行: $successCount / $totalCount 条SQL命令" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 数据库连接失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查数据库连接参数和服务状态" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
"@
