<template>
  <div class="app-container">
    <el-card class="upload-card">
      <div slot="header" class="clearfix">
        <span class="card-title">文档上传</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
      </div>

      <el-form ref="uploadForm" :model="uploadForm" :rules="uploadRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文档标题" prop="docTitle">
              <el-input v-model="uploadForm.docTitle" placeholder="请输入文档标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档分类" prop="categoryId">
              <el-cascader
                v-model="uploadForm.categoryId"
                :options="categoryOptions"
                :props="{ checkStrictly: true, value: 'categoryId', label: 'categoryName', children: 'children' }"
                placeholder="请选择文档分类"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文档作者" prop="author">
              <el-input v-model="uploadForm.author" placeholder="请输入文档作者" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布状态" prop="publishStatus">
              <el-select v-model="uploadForm.publishStatus" placeholder="请选择发布状态" style="width: 100%">
                <el-option label="草稿" value="0" />
                <el-option label="发布" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="文档标签" prop="tagIds">
          <el-select
            v-model="uploadForm.tagIds"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或创建标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in tagOptions"
              :key="tag.tagId"
              :label="tag.tagName"
              :value="tag.tagId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="关键词">
          <el-input v-model="uploadForm.keywords" placeholder="请输入关键词，多个关键词用逗号分隔" />
        </el-form-item>

        <el-form-item label="文档摘要">
          <el-input
            v-model="uploadForm.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入文档摘要"
          />
        </el-form-item>

        <el-form-item label="是否公开">
          <el-radio-group v-model="uploadForm.isPublic">
            <el-radio label="0">私有</el-radio>
            <el-radio label="1">公开</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="过期时间">
          <el-date-picker
            v-model="uploadForm.expireTime"
            type="datetime"
            placeholder="选择过期时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="文档文件" prop="file">
          <el-upload
            ref="upload"
            :limit="1"
            accept=".doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx,.txt,.md,.zip,.rar"
            :headers="upload.headers"
            :action="upload.url"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :on-error="handleFileError"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              支持格式：doc/docx/pdf/xls/xlsx/ppt/pptx/txt/md/zip/rar，且不超过100MB
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="uploadForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitUpload" :loading="upload.isUploading">
            <i class="el-icon-upload el-icon--left"></i>
            {{ upload.isUploading ? '上传中...' : '上传文档' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 上传进度对话框 -->
    <el-dialog title="上传进度" :visible.sync="upload.showProgress" width="400px" :close-on-click-modal="false">
      <el-progress :percentage="upload.percentage" :status="upload.status"></el-progress>
      <div style="margin-top: 15px; text-align: center;">
        <span>{{ upload.statusText }}</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { uploadDocument } from "@/api/document/document";
import { treeselect } from "@/api/document/category";
import { listTag } from "@/api/document/tag";
import { getToken } from "@/utils/auth";

export default {
  name: "DocumentUpload",
  data() {
    return {
      // 上传参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/document/doc/upload",
        // 上传进度
        percentage: 0,
        // 上传状态
        status: '',
        // 状态文本
        statusText: '准备上传...',
        // 显示进度对话框
        showProgress: false
      },
      // 表单参数
      uploadForm: {
        docTitle: '',
        categoryId: null,
        author: '',
        publishStatus: '0',
        tagIds: [],
        keywords: '',
        summary: '',
        isPublic: '0',
        expireTime: null,
        remark: '',
        file: null
      },
      // 表单校验
      uploadRules: {
        docTitle: [
          { required: true, message: "文档标题不能为空", trigger: "blur" }
        ],
        categoryId: [
          { required: true, message: "请选择文档分类", trigger: "change" }
        ],
        file: [
          { required: true, message: "请选择要上传的文件", trigger: "change" }
        ]
      },
      // 分类选项
      categoryOptions: [],
      // 标签选项
      tagOptions: []
    };
  },
  created() {
    this.getCategoryTree();
    this.getTagList();
  },
  methods: {
    /** 查询分类下拉树结构 */
    getCategoryTree() {
      treeselect().then(response => {
        this.categoryOptions = response.data;
      });
    },
    /** 查询标签列表 */
    getTagList() {
      listTag().then(response => {
        this.tagOptions = response.rows || [];
      });
    },
    /** 文件上传中处理 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
      this.upload.showProgress = true;
      this.upload.percentage = Math.round(event.loaded / event.total * 100);
      this.upload.statusText = `上传中... ${this.upload.percentage}%`;
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.upload.showProgress = false;
      if (response.code === 200) {
        this.$modal.msgSuccess("文档上传成功");
        this.$router.push('/document/list');
      } else {
        this.$modal.msgError(response.msg || "文档上传失败");
      }
    },
    /** 文件上传失败处理 */
    handleFileError(err, file, fileList) {
      this.upload.isUploading = false;
      this.upload.showProgress = false;
      this.$modal.msgError("文档上传失败");
    },
    /** 提交上传 */
    submitUpload() {
      this.$refs["uploadForm"].validate(valid => {
        if (valid) {
          const fileList = this.$refs.upload.uploadFiles;
          if (fileList.length === 0) {
            this.$modal.msgError("请选择要上传的文件");
            return;
          }
          
          // 构建FormData
          const formData = new FormData();
          formData.append('file', fileList[0].raw);
          
          // 添加表单数据
          Object.keys(this.uploadForm).forEach(key => {
            if (this.uploadForm[key] !== null && this.uploadForm[key] !== '') {
              if (key === 'tagIds' && Array.isArray(this.uploadForm[key])) {
                formData.append(key, this.uploadForm[key].join(','));
              } else {
                formData.append(key, this.uploadForm[key]);
              }
            }
          });

          this.upload.isUploading = true;
          this.upload.showProgress = true;
          this.upload.percentage = 0;
          this.upload.statusText = '开始上传...';

          uploadDocument(formData).then(response => {
            this.upload.isUploading = false;
            this.upload.showProgress = false;
            if (response.code === 200) {
              this.$modal.msgSuccess("文档上传成功");
              this.$router.push('/document/list');
            } else {
              this.$modal.msgError(response.msg || "文档上传失败");
            }
          }).catch(() => {
            this.upload.isUploading = false;
            this.upload.showProgress = false;
            this.$modal.msgError("文档上传失败");
          });
        }
      });
    },
    /** 重置表单 */
    resetForm() {
      this.uploadForm = {
        docTitle: '',
        categoryId: null,
        author: '',
        publishStatus: '0',
        tagIds: [],
        keywords: '',
        summary: '',
        isPublic: '0',
        expireTime: null,
        remark: '',
        file: null
      };
      this.$refs.upload.clearFiles();
      this.resetForm("uploadForm");
    },
    /** 返回列表 */
    goBack() {
      this.$router.push('/document/list');
    }
  }
};
</script>

<style scoped>
.upload-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.el-upload {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
  height: 180px;
}
</style>
