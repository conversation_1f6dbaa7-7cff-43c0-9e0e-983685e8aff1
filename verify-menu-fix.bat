@echo off
echo ========================================
echo RuoYi-Vue 工作流菜单修复验证脚本
echo ========================================
echo.

echo [1/3] 前端修复状态检查...
echo ----------------------------------------
echo 检查前端路由文件是否已移除静态工作流路由...

findstr /C:"工作流管理路由已移至动态路由" "RuoYi-Vue-master\ruoyi-ui\src\router\index.js" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ 前端静态工作流路由已成功移除
) else (
    echo ❌ 前端静态工作流路由可能未正确移除
)

findstr /C:"path: '/workflow'" "RuoYi-Vue-master\ruoyi-ui\src\router\index.js" >nul
if %ERRORLEVEL% neq 0 (
    echo ✅ 确认工作流路径已从静态路由中移除
) else (
    echo ⚠️ 静态路由中仍存在工作流路径
)
echo.

echo [2/3] 服务状态检查...
echo ----------------------------------------
echo 检查前端服务状态...
curl -s http://localhost:33334 >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ 前端服务运行正常 (http://localhost:33334)
) else (
    echo ❌ 前端服务无法访问
)

echo 检查后端服务状态...
curl -s http://localhost:55557 >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ 后端服务运行正常 (http://localhost:55557)
) else (
    echo ❌ 后端服务无法访问
)
echo.

echo [3/3] 修复建议...
echo ----------------------------------------
echo 📋 修复步骤总结：
echo 1. ✅ 已移除前端静态工作流路由
echo 2. ✅ 前端服务已重新编译
echo 3. 🔄 需要在浏览器中验证菜单显示
echo.
echo 🔍 验证步骤：
echo 1. 打开浏览器访问: http://localhost:33334
echo 2. 使用 admin/admin123 登录系统
echo 3. 检查左侧导航菜单中是否只有一个"工作流管理"菜单
echo 4. 点击工作流管理菜单，确认功能正常
echo.
echo 💡 如果仍有重复菜单，请执行以下操作：
echo 1. 运行数据库清理脚本: clean-duplicate-workflow-menu.sql
echo 2. 清除浏览器缓存并重新登录
echo 3. 或联系管理员检查数据库中的菜单数据
echo.

echo ========================================
echo 修复验证完成！
echo ========================================
echo.
echo 🌐 访问地址: http://localhost:33334
echo 👤 默认账号: admin
echo 🔑 默认密码: admin123
echo.

pause
