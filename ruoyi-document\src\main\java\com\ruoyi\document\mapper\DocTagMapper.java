package com.ruoyi.document.mapper;

import java.util.List;
import com.ruoyi.document.domain.DocTag;
import org.apache.ibatis.annotations.Param;

/**
 * 文档标签Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface DocTagMapper 
{
    /**
     * 查询文档标签
     * 
     * @param tagId 文档标签主键
     * @return 文档标签
     */
    public DocTag selectDocTagByTagId(Long tagId);

    /**
     * 查询文档标签列表
     * 
     * @param docTag 文档标签
     * @return 文档标签集合
     */
    public List<DocTag> selectDocTagList(DocTag docTag);

    /**
     * 新增文档标签
     * 
     * @param docTag 文档标签
     * @return 结果
     */
    public int insertDocTag(DocTag docTag);

    /**
     * 修改文档标签
     * 
     * @param docTag 文档标签
     * @return 结果
     */
    public int updateDocTag(DocTag docTag);

    /**
     * 删除文档标签
     * 
     * @param tagId 文档标签主键
     * @return 结果
     */
    public int deleteDocTagByTagId(Long tagId);

    /**
     * 批量删除文档标签
     * 
     * @param tagIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDocTagByTagIds(Long[] tagIds);

    /**
     * 根据文档ID查询标签列表
     * 
     * @param docId 文档ID
     * @return 标签集合
     */
    public List<DocTag> selectTagsByDocId(Long docId);

    /**
     * 检查标签名称是否唯一
     * 
     * @param docTag 标签信息
     * @return 结果
     */
    public DocTag checkTagNameUnique(DocTag docTag);

    /**
     * 更新标签使用次数
     * 
     * @param tagId 标签ID
     * @param increment 增量（可为负数）
     * @return 结果
     */
    public int updateTagUseCount(@Param("tagId") Long tagId, @Param("increment") Integer increment);

    /**
     * 获取热门标签
     * 
     * @param limit 限制数量
     * @return 标签集合
     */
    public List<DocTag> selectHotTags(@Param("limit") Integer limit);
}
