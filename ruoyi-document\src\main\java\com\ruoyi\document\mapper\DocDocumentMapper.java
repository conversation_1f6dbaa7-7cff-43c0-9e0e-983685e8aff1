package com.ruoyi.document.mapper;

import java.util.List;
import com.ruoyi.document.domain.DocDocument;
import org.apache.ibatis.annotations.Param;

/**
 * 文档管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface DocDocumentMapper 
{
    /**
     * 查询文档
     * 
     * @param docId 文档主键
     * @return 文档
     */
    public DocDocument selectDocDocumentByDocId(Long docId);

    /**
     * 查询文档列表
     * 
     * @param docDocument 文档
     * @return 文档集合
     */
    public List<DocDocument> selectDocDocumentList(DocDocument docDocument);

    /**
     * 新增文档
     * 
     * @param docDocument 文档
     * @return 结果
     */
    public int insertDocDocument(DocDocument docDocument);

    /**
     * 修改文档
     * 
     * @param docDocument 文档
     * @return 结果
     */
    public int updateDocDocument(DocDocument docDocument);

    /**
     * 删除文档
     * 
     * @param docId 文档主键
     * @return 结果
     */
    public int deleteDocDocumentByDocId(Long docId);

    /**
     * 批量删除文档
     * 
     * @param docIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDocDocumentByDocIds(Long[] docIds);

    /**
     * 根据分类ID查询文档数量
     * 
     * @param categoryId 分类ID
     * @return 文档数量
     */
    public int countDocumentsByCategoryId(Long categoryId);

    /**
     * 全文搜索文档
     * 
     * @param keyword 搜索关键词
     * @param docDocument 查询条件
     * @return 文档集合
     */
    public List<DocDocument> searchDocuments(@Param("keyword") String keyword, @Param("doc") DocDocument docDocument);

    /**
     * 更新文档查看次数
     * 
     * @param docId 文档ID
     * @return 结果
     */
    public int updateViewCount(Long docId);

    /**
     * 更新文档下载次数
     * 
     * @param docId 文档ID
     * @return 结果
     */
    public int updateDownloadCount(Long docId);

    /**
     * 根据用户ID查询收藏的文档
     * 
     * @param userId 用户ID
     * @param docDocument 查询条件
     * @return 文档集合
     */
    public List<DocDocument> selectFavoriteDocuments(@Param("userId") Long userId, @Param("doc") DocDocument docDocument);

    /**
     * 根据用户ID查询最近访问的文档
     * 
     * @param userId 用户ID
     * @param docDocument 查询条件
     * @return 文档集合
     */
    public List<DocDocument> selectRecentDocuments(@Param("userId") Long userId, @Param("doc") DocDocument docDocument);

    /**
     * 检查文档是否存在（根据MD5）
     * 
     * @param fileMd5 文件MD5值
     * @return 文档信息
     */
    public DocDocument selectDocumentByMd5(String fileMd5);

    /**
     * 批量插入文档标签关联
     * 
     * @param docId 文档ID
     * @param tagIds 标签ID列表
     * @return 结果
     */
    public int batchInsertDocumentTags(@Param("docId") Long docId, @Param("tagIds") List<Long> tagIds);

    /**
     * 删除文档标签关联
     * 
     * @param docId 文档ID
     * @return 结果
     */
    public int deleteDocumentTags(Long docId);

    /**
     * 根据标签ID查询文档
     * 
     * @param tagIds 标签ID列表
     * @param docDocument 查询条件
     * @return 文档集合
     */
    public List<DocDocument> selectDocumentsByTagIds(@Param("tagIds") List<Long> tagIds, @Param("doc") DocDocument docDocument);
}
