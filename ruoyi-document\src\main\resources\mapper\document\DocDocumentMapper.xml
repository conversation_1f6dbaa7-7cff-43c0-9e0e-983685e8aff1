<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.document.mapper.DocDocumentMapper">
    
    <resultMap type="DocDocument" id="DocDocumentResult">
        <result property="docId"    column="doc_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="docTitle"    column="doc_title"    />
        <result property="docCode"    column="doc_code"    />
        <result property="docType"    column="doc_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileMd5"    column="file_md5"    />
        <result property="content"    column="content"    />
        <result property="summary"    column="summary"    />
        <result property="keywords"    column="keywords"    />
        <result property="author"    column="author"    />
        <result property="versionNum"    column="version_num"    />
        <result property="currentVersionId"    column="current_version_id"    />
        <result property="publishStatus"    column="publish_status"    />
        <result property="viewCount"    column="view_count"    />
        <result property="downloadCount"    column="download_count"    />
        <result property="isPublic"    column="is_public"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="categoryName"    column="category_name"    />
    </resultMap>

    <sql id="selectDocDocumentVo">
        select d.doc_id, d.category_id, d.doc_title, d.doc_code, d.doc_type, d.file_name, d.file_path, 
               d.file_size, d.file_md5, d.content, d.summary, d.keywords, d.author, d.version_num, 
               d.current_version_id, d.publish_status, d.view_count, d.download_count, d.is_public, 
               d.expire_time, d.status, d.del_flag, d.create_by, d.create_time, d.update_by, 
               d.update_time, d.remark, c.category_name
        from doc_document d
        left join doc_category c on d.category_id = c.category_id
    </sql>

    <select id="selectDocDocumentList" parameterType="DocDocument" resultMap="DocDocumentResult">
        <include refid="selectDocDocumentVo"/>
        <where>  
            d.del_flag = '0'
            <if test="categoryId != null "> and d.category_id = #{categoryId}</if>
            <if test="docTitle != null  and docTitle != ''"> and d.doc_title like concat('%', #{docTitle}, '%')</if>
            <if test="docCode != null  and docCode != ''"> and d.doc_code = #{docCode}</if>
            <if test="docType != null  and docType != ''"> and d.doc_type = #{docType}</if>
            <if test="publishStatus != null  and publishStatus != ''"> and d.publish_status = #{publishStatus}</if>
            <if test="author != null  and author != ''"> and d.author like concat('%', #{author}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and d.create_by like concat('%', #{createBy}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(d.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(d.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by d.create_time desc
    </select>
    
    <select id="selectDocDocumentByDocId" parameterType="Long" resultMap="DocDocumentResult">
        <include refid="selectDocDocumentVo"/>
        where d.doc_id = #{docId} and d.del_flag = '0'
    </select>
        
    <insert id="insertDocDocument" parameterType="DocDocument" useGeneratedKeys="true" keyProperty="docId">
        insert into doc_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="docTitle != null and docTitle != ''">doc_title,</if>
            <if test="docCode != null">doc_code,</if>
            <if test="docType != null and docType != ''">doc_type,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="filePath != null and filePath != ''">file_path,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileMd5 != null">file_md5,</if>
            <if test="content != null">content,</if>
            <if test="summary != null">summary,</if>
            <if test="keywords != null">keywords,</if>
            <if test="author != null">author,</if>
            <if test="versionNum != null">version_num,</if>
            <if test="currentVersionId != null">current_version_id,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="downloadCount != null">download_count,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="docTitle != null and docTitle != ''">#{docTitle},</if>
            <if test="docCode != null">#{docCode},</if>
            <if test="docType != null and docType != ''">#{docType},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="filePath != null and filePath != ''">#{filePath},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileMd5 != null">#{fileMd5},</if>
            <if test="content != null">#{content},</if>
            <if test="summary != null">#{summary},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="author != null">#{author},</if>
            <if test="versionNum != null">#{versionNum},</if>
            <if test="currentVersionId != null">#{currentVersionId},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="downloadCount != null">#{downloadCount},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDocDocument" parameterType="DocDocument">
        update doc_document
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="docTitle != null and docTitle != ''">doc_title = #{docTitle},</if>
            <if test="docCode != null">doc_code = #{docCode},</if>
            <if test="docType != null and docType != ''">doc_type = #{docType},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="filePath != null and filePath != ''">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileMd5 != null">file_md5 = #{fileMd5},</if>
            <if test="content != null">content = #{content},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="author != null">author = #{author},</if>
            <if test="versionNum != null">version_num = #{versionNum},</if>
            <if test="currentVersionId != null">current_version_id = #{currentVersionId},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where doc_id = #{docId}
    </update>

    <delete id="deleteDocDocumentByDocId" parameterType="Long">
        update doc_document set del_flag = '2' where doc_id = #{docId}
    </delete>

    <delete id="deleteDocDocumentByDocIds" parameterType="String">
        update doc_document set del_flag = '2' where doc_id in 
        <foreach item="docId" collection="array" open="(" separator="," close=")">
            #{docId}
        </foreach>
    </delete>

    <select id="countDocumentsByCategoryId" parameterType="Long" resultType="int">
        select count(*) from doc_document where category_id = #{categoryId} and del_flag = '0'
    </select>

    <select id="searchDocuments" resultMap="DocDocumentResult">
        <include refid="selectDocDocumentVo"/>
        <where>
            d.del_flag = '0'
            <if test="keyword != null and keyword != ''">
                and (d.doc_title like concat('%', #{keyword}, '%') 
                     or d.content like concat('%', #{keyword}, '%')
                     or d.summary like concat('%', #{keyword}, '%')
                     or d.keywords like concat('%', #{keyword}, '%'))
            </if>
            <if test="doc.categoryId != null">and d.category_id = #{doc.categoryId}</if>
            <if test="doc.docType != null and doc.docType != ''">and d.doc_type = #{doc.docType}</if>
            <if test="doc.publishStatus != null and doc.publishStatus != ''">and d.publish_status = #{doc.publishStatus}</if>
        </where>
        order by d.create_time desc
    </select>

    <update id="updateViewCount" parameterType="Long">
        update doc_document set view_count = view_count + 1 where doc_id = #{docId}
    </update>

    <update id="updateDownloadCount" parameterType="Long">
        update doc_document set download_count = download_count + 1 where doc_id = #{docId}
    </update>

    <select id="selectDocumentByMd5" parameterType="String" resultMap="DocDocumentResult">
        <include refid="selectDocDocumentVo"/>
        where d.file_md5 = #{fileMd5} and d.del_flag = '0'
    </select>

    <insert id="batchInsertDocumentTags">
        insert into doc_document_tag (doc_id, tag_id) values
        <foreach collection="tagIds" item="tagId" separator=",">
            (#{docId}, #{tagId})
        </foreach>
    </insert>

    <delete id="deleteDocumentTags" parameterType="Long">
        delete from doc_document_tag where doc_id = #{docId}
    </delete>

    <select id="selectFavoriteDocuments" resultMap="DocDocumentResult">
        <include refid="selectDocDocumentVo"/>
        inner join doc_favorite f on d.doc_id = f.doc_id
        <where>
            d.del_flag = '0' and f.user_id = #{userId}
            <if test="doc.categoryId != null">and d.category_id = #{doc.categoryId}</if>
            <if test="doc.docTitle != null and doc.docTitle != ''">and d.doc_title like concat('%', #{doc.docTitle}, '%')</if>
        </where>
        order by f.create_time desc
    </select>

    <select id="selectRecentDocuments" resultMap="DocDocumentResult">
        <include refid="selectDocDocumentVo"/>
        inner join doc_operation_log l on d.doc_id = l.doc_id
        <where>
            d.del_flag = '0' and l.user_id = #{userId} and l.operation_type = 'view'
            <if test="doc.categoryId != null">and d.category_id = #{doc.categoryId}</if>
        </where>
        group by d.doc_id
        order by max(l.operation_time) desc
    </select>

    <select id="selectDocumentsByTagIds" resultMap="DocDocumentResult">
        <include refid="selectDocDocumentVo"/>
        inner join doc_document_tag dt on d.doc_id = dt.doc_id
        <where>
            d.del_flag = '0'
            <if test="tagIds != null and tagIds.size() > 0">
                and dt.tag_id in
                <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
            <if test="doc.categoryId != null">and d.category_id = #{doc.categoryId}</if>
            <if test="doc.publishStatus != null and doc.publishStatus != ''">and d.publish_status = #{doc.publishStatus}</if>
        </where>
        group by d.doc_id
        order by d.create_time desc
    </select>

</mapper>
