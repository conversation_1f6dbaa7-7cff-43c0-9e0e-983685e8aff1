<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.document.mapper.DocCategoryMapper">
    
    <resultMap type="DocCategory" id="DocCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="orderNum"    column="order_num"    />
        <result property="description"    column="description"    />
        <result property="icon"    column="icon"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="parentName"    column="parent_name"    />
    </resultMap>

    <sql id="selectDocCategoryVo">
        select c.category_id, c.parent_id, c.ancestors, c.category_name, c.category_code, c.order_num, 
               c.description, c.icon, c.status, c.del_flag, c.create_by, c.create_time, c.update_by, 
               c.update_time, c.remark, p.category_name as parent_name
        from doc_category c
        left join doc_category p on c.parent_id = p.category_id
    </sql>

    <select id="selectDocCategoryList" parameterType="DocCategory" resultMap="DocCategoryResult">
        <include refid="selectDocCategoryVo"/>
        <where>  
            c.del_flag = '0'
            <if test="categoryId != null "> and c.category_id = #{categoryId}</if>
            <if test="parentId != null "> and c.parent_id = #{parentId}</if>
            <if test="categoryName != null  and categoryName != ''"> and c.category_name like concat('%', #{categoryName}, '%')</if>
            <if test="categoryCode != null  and categoryCode != ''"> and c.category_code = #{categoryCode}</if>
            <if test="status != null  and status != ''"> and c.status = #{status}</if>
        </where>
        order by c.parent_id, c.order_num
    </select>
    
    <select id="selectDocCategoryByCategoryId" parameterType="Long" resultMap="DocCategoryResult">
        <include refid="selectDocCategoryVo"/>
        where c.category_id = #{categoryId} and c.del_flag = '0'
    </select>
        
    <insert id="insertDocCategory" parameterType="DocCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into doc_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categoryCode != null">category_code,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="description != null">description,</if>
            <if test="icon != null">icon,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categoryCode != null">#{categoryCode},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="description != null">#{description},</if>
            <if test="icon != null">#{icon},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDocCategory" parameterType="DocCategory">
        update doc_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categoryCode != null">category_code = #{categoryCode},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="description != null">description = #{description},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteDocCategoryByCategoryId" parameterType="Long">
        update doc_category set del_flag = '2' where category_id = #{categoryId}
    </delete>

    <delete id="deleteDocCategoryByCategoryIds" parameterType="String">
        update doc_category set del_flag = '2' where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="selectChildrenCountById" parameterType="Long" resultType="int">
        select count(*) from doc_category where parent_id = #{categoryId} and del_flag = '0'
    </select>

    <select id="selectChildrenCategoryById" parameterType="Long" resultMap="DocCategoryResult">
        <include refid="selectDocCategoryVo"/>
        where c.parent_id = #{categoryId} and c.del_flag = '0'
        order by c.order_num
    </select>

    <update id="updateCategoryChildren" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update doc_category set ancestors = #{item.ancestors} where category_id = #{item.categoryId}
        </foreach>
    </update>

    <select id="checkCategoryNameUnique" parameterType="DocCategory" resultMap="DocCategoryResult">
        <include refid="selectDocCategoryVo"/>
        where c.category_name = #{categoryName} and c.parent_id = #{parentId} and c.del_flag = '0'
        <if test="categoryId != null">
            and c.category_id != #{categoryId}
        </if>
        limit 1
    </select>

    <select id="selectCategoryListByRoleId" parameterType="Long" resultType="Long">
        select distinct c.category_id
        from doc_category c
        left join doc_category_role cr on c.category_id = cr.category_id
        where c.del_flag = '0' and c.status = '0'
        and (cr.role_id = #{roleId} or c.category_id in (
            select category_id from doc_category where del_flag = '0' and status = '0'
        ))
    </select>

    <select id="selectCategoryListByUserId" parameterType="Long" resultType="Long">
        select distinct c.category_id
        from doc_category c
        left join doc_category_user cu on c.category_id = cu.category_id
        left join doc_category_role cr on c.category_id = cr.category_id
        left join sys_user_role ur on cr.role_id = ur.role_id
        where c.del_flag = '0' and c.status = '0'
        and (cu.user_id = #{userId} or ur.user_id = #{userId} or c.category_id in (
            select category_id from doc_category where del_flag = '0' and status = '0'
        ))
    </select>

</mapper>
