package com.ruoyi.document.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文档对象 doc_document
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public class DocDocument extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文档ID */
    private Long docId;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 文档标题 */
    @Excel(name = "文档标题")
    private String docTitle;

    /** 文档编号 */
    @Excel(name = "文档编号")
    private String docCode;

    /** 文档类型 */
    @Excel(name = "文档类型")
    private String docType;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    /** 文件大小（字节） */
    @Excel(name = "文件大小", readConverterExp = "字=节")
    private Long fileSize;

    /** 文件MD5值 */
    private String fileMd5;

    /** 文档内容（用于全文检索） */
    private String content;

    /** 文档摘要 */
    @Excel(name = "文档摘要")
    private String summary;

    /** 关键词 */
    @Excel(name = "关键词")
    private String keywords;

    /** 文档作者 */
    @Excel(name = "文档作者")
    private String author;

    /** 版本号 */
    @Excel(name = "版本号")
    private String versionNum;

    /** 当前版本ID */
    private Long currentVersionId;

    /** 发布状态（0草稿 1发布 2归档） */
    @Excel(name = "发布状态", readConverterExp = "0=草稿,1=发布,2=归档")
    private String publishStatus;

    /** 查看次数 */
    @Excel(name = "查看次数")
    private Integer viewCount;

    /** 下载次数 */
    @Excel(name = "下载次数")
    private Integer downloadCount;

    /** 是否公开（0私有 1公开） */
    @Excel(name = "是否公开", readConverterExp = "0=私有,1=公开")
    private String isPublic;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 分类名称 */
    private String categoryName;

    /** 标签列表 */
    private List<DocTag> tags;

    /** 标签ID列表 */
    private List<Long> tagIds;

    /** 权限级别 */
    private String permissionLevel;

    /** 是否收藏 */
    private Boolean isFavorite;

    public void setDocId(Long docId) 
    {
        this.docId = docId;
    }

    public Long getDocId() 
    {
        return docId;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }

    public void setDocTitle(String docTitle) 
    {
        this.docTitle = docTitle;
    }

    public String getDocTitle() 
    {
        return docTitle;
    }

    public void setDocCode(String docCode) 
    {
        this.docCode = docCode;
    }

    public String getDocCode() 
    {
        return docCode;
    }

    public void setDocType(String docType) 
    {
        this.docType = docType;
    }

    public String getDocType() 
    {
        return docType;
    }

    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }

    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }

    public void setFileSize(Long fileSize) 
    {
        this.fileSize = fileSize;
    }

    public Long getFileSize() 
    {
        return fileSize;
    }

    public void setFileMd5(String fileMd5) 
    {
        this.fileMd5 = fileMd5;
    }

    public String getFileMd5() 
    {
        return fileMd5;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setSummary(String summary) 
    {
        this.summary = summary;
    }

    public String getSummary() 
    {
        return summary;
    }

    public void setKeywords(String keywords) 
    {
        this.keywords = keywords;
    }

    public String getKeywords() 
    {
        return keywords;
    }

    public void setAuthor(String author) 
    {
        this.author = author;
    }

    public String getAuthor() 
    {
        return author;
    }

    public void setVersionNum(String versionNum) 
    {
        this.versionNum = versionNum;
    }

    public String getVersionNum() 
    {
        return versionNum;
    }

    public void setCurrentVersionId(Long currentVersionId) 
    {
        this.currentVersionId = currentVersionId;
    }

    public Long getCurrentVersionId() 
    {
        return currentVersionId;
    }

    public void setPublishStatus(String publishStatus) 
    {
        this.publishStatus = publishStatus;
    }

    public String getPublishStatus() 
    {
        return publishStatus;
    }

    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }

    public void setDownloadCount(Integer downloadCount) 
    {
        this.downloadCount = downloadCount;
    }

    public Integer getDownloadCount() 
    {
        return downloadCount;
    }

    public void setIsPublic(String isPublic) 
    {
        this.isPublic = isPublic;
    }

    public String getIsPublic() 
    {
        return isPublic;
    }

    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public List<DocTag> getTags() {
        return tags;
    }

    public void setTags(List<DocTag> tags) {
        this.tags = tags;
    }

    public List<Long> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<Long> tagIds) {
        this.tagIds = tagIds;
    }

    public String getPermissionLevel() {
        return permissionLevel;
    }

    public void setPermissionLevel(String permissionLevel) {
        this.permissionLevel = permissionLevel;
    }

    public Boolean getIsFavorite() {
        return isFavorite;
    }

    public void setIsFavorite(Boolean isFavorite) {
        this.isFavorite = isFavorite;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("docId", getDocId())
            .append("categoryId", getCategoryId())
            .append("docTitle", getDocTitle())
            .append("docCode", getDocCode())
            .append("docType", getDocType())
            .append("fileName", getFileName())
            .append("filePath", getFilePath())
            .append("fileSize", getFileSize())
            .append("fileMd5", getFileMd5())
            .append("content", getContent())
            .append("summary", getSummary())
            .append("keywords", getKeywords())
            .append("author", getAuthor())
            .append("versionNum", getVersionNum())
            .append("currentVersionId", getCurrentVersionId())
            .append("publishStatus", getPublishStatus())
            .append("viewCount", getViewCount())
            .append("downloadCount", getDownloadCount())
            .append("isPublic", getIsPublic())
            .append("expireTime", getExpireTime())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
