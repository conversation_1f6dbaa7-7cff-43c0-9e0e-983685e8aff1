-- ========================================
-- 请在数据库管理工具中执行此脚本
-- 数据库: ry-vue
-- ========================================

USE `ry-vue`;

-- ========================================
-- 1. 创建文档分类表
-- ========================================
DROP TABLE IF EXISTS `doc_category`;
CREATE TABLE `doc_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `ancestors` varchar(500) DEFAULT '' COMMENT '祖级列表',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) DEFAULT NULL COMMENT '分类编码',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `description` varchar(500) DEFAULT '' COMMENT '分类描述',
  `icon` varchar(100) DEFAULT '' COMMENT '分类图标',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_code` (`category_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档分类表';

-- ========================================
-- 2. 创建文档标签表
-- ========================================
DROP TABLE IF EXISTS `doc_tag`;
CREATE TABLE `doc_tag` (
  `tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_color` varchar(20) DEFAULT '#409EFF' COMMENT '标签颜色',
  `description` varchar(200) DEFAULT '' COMMENT '标签描述',
  `use_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`tag_id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档标签表';

-- ========================================
-- 3. 创建文档主表
-- ========================================
DROP TABLE IF EXISTS `doc_document`;
CREATE TABLE `doc_document` (
  `doc_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `doc_title` varchar(200) NOT NULL COMMENT '文档标题',
  `doc_code` varchar(100) DEFAULT NULL COMMENT '文档编号',
  `doc_type` varchar(20) NOT NULL COMMENT '文档类型',
  `file_name` varchar(200) NOT NULL COMMENT '文件名称',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `file_md5` varchar(32) DEFAULT NULL COMMENT '文件MD5值',
  `content` longtext COMMENT '文档内容',
  `summary` varchar(1000) DEFAULT '' COMMENT '文档摘要',
  `keywords` varchar(500) DEFAULT '' COMMENT '关键词',
  `author` varchar(100) DEFAULT '' COMMENT '文档作者',
  `version_num` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `current_version_id` bigint(20) DEFAULT NULL COMMENT '当前版本ID',
  `publish_status` char(1) DEFAULT '0' COMMENT '发布状态（0草稿 1发布 2归档）',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `is_public` char(1) DEFAULT '0' COMMENT '是否公开（0私有 1公开）',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`doc_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_doc_code` (`doc_code`),
  KEY `idx_doc_type` (`doc_type`),
  KEY `idx_publish_status` (`publish_status`),
  KEY `idx_create_by` (`create_by`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_file_md5` (`file_md5`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档主表';

-- ========================================
-- 4. 创建文档标签关联表
-- ========================================
DROP TABLE IF EXISTS `doc_document_tag`;
CREATE TABLE `doc_document_tag` (
  `doc_id` bigint(20) NOT NULL COMMENT '文档ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  PRIMARY KEY (`doc_id`,`tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档标签关联表';

-- ========================================
-- 5. 插入初始化分类数据
-- ========================================
INSERT INTO `doc_category` VALUES 
(1000, 0, '0', '技术文档', 'TECH_DOC', 1, '技术相关文档分类', 'documentation', '0', '0', 'admin', NOW(), '', NULL, '技术文档根分类'),
(1001, 1000, '0,1000', '开发文档', 'DEV_DOC', 1, '开发相关文档', 'code', '0', '0', 'admin', NOW(), '', NULL, '开发文档'),
(1002, 1000, '0,1000', '运维文档', 'OPS_DOC', 2, '运维相关文档', 'monitor', '0', '0', 'admin', NOW(), '', NULL, '运维文档'),
(1003, 1000, '0,1000', 'API文档', 'API_DOC', 3, 'API接口文档', 'link', '0', '0', 'admin', NOW(), '', NULL, 'API文档'),
(1004, 0, '0', '管理文档', 'MGMT_DOC', 2, '管理制度文档', 'system', '0', '0', 'admin', NOW(), '', NULL, '管理文档根分类'),
(1005, 1004, '0,1004', '制度规范', 'RULE_DOC', 1, '公司制度规范', 'education', '0', '0', 'admin', NOW(), '', NULL, '制度规范'),
(1006, 1004, '0,1004', '流程文档', 'PROCESS_DOC', 2, '业务流程文档', 'tree-table', '0', '0', 'admin', NOW(), '', NULL, '流程文档');

-- ========================================
-- 6. 插入初始化标签数据
-- ========================================
INSERT INTO `doc_tag` VALUES 
(1000, '重要', '#F56C6C', '重要文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1001, '紧急', '#E6A23C', '紧急文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1002, '草稿', '#909399', '草稿文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1003, '已发布', '#67C23A', '已发布文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1004, '待审核', '#409EFF', '待审核文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1005, '已归档', '#909399', '已归档文档标签', 0, '0', 'admin', NOW(), '', NULL),
(1006, 'Java', '#FF6B35', 'Java技术标签', 0, '0', 'admin', NOW(), '', NULL),
(1007, 'Vue', '#4FC08D', 'Vue技术标签', 0, '0', 'admin', NOW(), '', NULL),
(1008, 'MySQL', '#00758F', 'MySQL技术标签', 0, '0', 'admin', NOW(), '', NULL),
(1009, 'Spring Boot', '#6DB33F', 'Spring Boot技术标签', 0, '0', 'admin', NOW(), '', NULL);

-- ========================================
-- 7. 添加文档管理菜单
-- ========================================

-- 删除可能存在的文档管理菜单
DELETE FROM sys_role_menu WHERE menu_id >= 3000 AND menu_id <= 3020;
DELETE FROM sys_menu WHERE menu_id >= 3000 AND menu_id <= 3020;

-- 一级菜单：文档管理
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3000, '文档管理', 0, 6, 'document', NULL, '', 1, 0, 'M', '0', '0', '', 'documentation', 'admin', NOW(), '文档管理目录');

-- 二级菜单：文档列表
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3001, '文档列表', 3000, 1, 'list', 'document/list/index', '', 1, 0, 'C', '0', '0', 'document:list:view', 'list', 'admin', NOW(), '文档列表菜单');

-- 二级菜单：文档上传
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3002, '文档上传', 3000, 2, 'upload', 'document/upload/index', '', 1, 0, 'C', '0', '0', 'document:upload:view', 'upload', 'admin', NOW(), '文档上传菜单');

-- 二级菜单：分类管理
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3003, '分类管理', 3000, 3, 'category', 'document/category/index', '', 1, 0, 'C', '0', '0', 'document:category:view', 'tree', 'admin', NOW(), '分类管理菜单');

-- 二级菜单：标签管理
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3004, '标签管理', 3000, 4, 'tag', 'document/tag/index', '', 1, 0, 'C', '0', '0', 'document:tag:view', 'price-tag', 'admin', NOW(), '标签管理菜单');

-- 文档操作按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3010, '文档查询', 3001, 1, '', '', '', 1, 0, 'F', '0', '0', 'document:list:query', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3011, '文档新增', 3001, 2, '', '', '', 1, 0, 'F', '0', '0', 'document:list:add', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3012, '文档修改', 3001, 3, '', '', '', 1, 0, 'F', '0', '0', 'document:list:edit', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3013, '文档删除', 3001, 4, '', '', '', 1, 0, 'F', '0', '0', 'document:list:remove', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3014, '文档下载', 3001, 5, '', '', '', 1, 0, 'F', '0', '0', 'document:list:download', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3015, '文档分享', 3001, 6, '', '', '', 1, 0, 'F', '0', '0', 'document:list:share', '#', 'admin', NOW(), '');

-- 分类管理按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3016, '分类新增', 3003, 1, '', '', '', 1, 0, 'F', '0', '0', 'document:category:add', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3017, '分类修改', 3003, 2, '', '', '', 1, 0, 'F', '0', '0', 'document:category:edit', '#', 'admin', NOW(), '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (3018, '分类删除', 3003, 3, '', '', '', 1, 0, 'F', '0', '0', 'document:category:remove', '#', 'admin', NOW(), '');

-- 为超级管理员角色分配文档管理权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_id >= 3000 AND menu_id <= 3020;

-- ========================================
-- 8. 添加文档配置参数
-- ========================================
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
VALUES 
('文档上传路径', 'document.upload.path', '/profile/document', 'Y', 'admin', NOW(), '文档上传保存路径'),
('文档最大上传大小', 'document.upload.maxSize', '100', 'Y', 'admin', NOW(), '文档最大上传大小（MB）'),
('允许上传的文档类型', 'document.upload.allowTypes', 'doc,docx,pdf,xls,xlsx,ppt,pptx,txt,md,zip,rar', 'Y', 'admin', NOW(), '允许上传的文档文件类型'),
('文档版本保留数量', 'document.version.maxCount', '10', 'Y', 'admin', NOW(), '文档版本最大保留数量'),
('文档全文检索开关', 'document.search.enabled', 'true', 'Y', 'admin', NOW(), '是否启用文档全文检索功能');

SELECT '✅ 文档管理系统数据库初始化完成！' AS message;
