-- 清理重复的工作流菜单
USE `ry-vue`;

-- 开始事务，确保数据安全
START TRANSACTION;

-- 1. 备份当前工作流菜单数据（可选）
CREATE TABLE IF NOT EXISTS sys_menu_backup_workflow AS
SELECT * FROM sys_menu 
WHERE menu_name LIKE '%工作流%' 
   OR menu_name LIKE '%流程%' 
   OR menu_name LIKE '%任务%'
   OR menu_name LIKE '%设计器%'
   OR menu_name LIKE '%监控%'
   OR menu_name LIKE '%版本%'
   OR path LIKE '%workflow%';

-- 2. 删除重复的工作流管理主菜单（保留ID最小的）
-- 首先删除重复菜单的角色权限
DELETE FROM sys_role_menu 
WHERE menu_id IN (
    SELECT menu_id FROM sys_menu 
    WHERE menu_name = '工作流管理' 
      AND menu_id NOT IN (
          SELECT * FROM (
              SELECT MIN(menu_id) 
              FROM sys_menu 
              WHERE menu_name = '工作流管理'
          ) AS temp
      )
);

-- 删除重复菜单的子菜单的角色权限
DELETE FROM sys_role_menu 
WHERE menu_id IN (
    SELECT menu_id FROM sys_menu 
    WHERE parent_id IN (
        SELECT menu_id FROM sys_menu 
        WHERE menu_name = '工作流管理' 
          AND menu_id NOT IN (
              SELECT * FROM (
                  SELECT MIN(menu_id) 
                  FROM sys_menu 
                  WHERE menu_name = '工作流管理'
              ) AS temp
          )
    )
);

-- 删除重复菜单的子菜单
DELETE FROM sys_menu 
WHERE parent_id IN (
    SELECT * FROM (
        SELECT menu_id FROM sys_menu 
        WHERE menu_name = '工作流管理' 
          AND menu_id NOT IN (
              SELECT MIN(menu_id) 
              FROM sys_menu 
              WHERE menu_name = '工作流管理'
          )
    ) AS temp
);

-- 删除重复的工作流管理主菜单
DELETE FROM sys_menu 
WHERE menu_name = '工作流管理' 
  AND menu_id NOT IN (
      SELECT * FROM (
          SELECT MIN(menu_id) 
          FROM sys_menu 
          WHERE menu_name = '工作流管理'
      ) AS temp
  );

-- 3. 确保工作流菜单数据完整
-- 如果没有工作流管理主菜单，则创建一个
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES (2000, '工作流管理', 0, 5, 'workflow', NULL, '', 1, 0, 'M', '0', '0', '', 'cascader', 'admin', NOW(), '工作流管理目录');

-- 4. 确保超级管理员有工作流菜单权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu 
WHERE menu_name = '工作流管理' 
   OR parent_id IN (SELECT menu_id FROM sys_menu WHERE menu_name = '工作流管理');

-- 5. 验证修复结果
SELECT '=== 修复后的工作流菜单状态 ===' AS info;
SELECT 
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    status,
    visible
FROM sys_menu 
WHERE menu_name LIKE '%工作流%' 
   OR menu_name LIKE '%流程%' 
   OR menu_name LIKE '%任务%'
   OR menu_name LIKE '%设计器%'
   OR menu_name LIKE '%监控%'
   OR menu_name LIKE '%版本%'
   OR path LIKE '%workflow%'
ORDER BY parent_id, order_num;

-- 提交事务
COMMIT;

SELECT '✅ 工作流菜单重复问题修复完成！请重新登录系统查看效果。' AS result;
