package com.ruoyi.document.service;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.document.domain.DocDocument;
import com.ruoyi.document.domain.DocVersion;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 文档管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IDocDocumentService 
{
    /**
     * 查询文档
     * 
     * @param docId 文档主键
     * @return 文档
     */
    public DocDocument selectDocDocumentByDocId(Long docId);

    /**
     * 查询文档列表
     * 
     * @param docDocument 文档
     * @return 文档集合
     */
    public List<DocDocument> selectDocDocumentList(DocDocument docDocument);

    /**
     * 新增文档
     * 
     * @param docDocument 文档
     * @return 结果
     */
    public int insertDocDocument(DocDocument docDocument);

    /**
     * 修改文档
     * 
     * @param docDocument 文档
     * @return 结果
     */
    public int updateDocDocument(DocDocument docDocument);

    /**
     * 批量删除文档
     * 
     * @param docIds 需要删除的文档主键集合
     * @return 结果
     */
    public int deleteDocDocumentByDocIds(Long[] docIds);

    /**
     * 删除文档信息
     * 
     * @param docId 文档主键
     * @return 结果
     */
    public int deleteDocDocumentByDocId(Long docId);

    /**
     * 上传文档
     * 
     * @param file 上传的文件
     * @param docDocument 文档信息
     * @return 结果
     */
    public AjaxResult uploadDocument(MultipartFile file, DocDocument docDocument);

    /**
     * 下载文档
     * 
     * @param docId 文档ID
     * @param response HTTP响应
     */
    public void downloadDocument(Long docId, HttpServletResponse response);

    /**
     * 预览文档
     * 
     * @param docId 文档ID
     * @return 预览结果
     */
    public AjaxResult previewDocument(Long docId);

    /**
     * 全文搜索文档
     * 
     * @param keyword 搜索关键词
     * @param docDocument 查询条件
     * @return 文档集合
     */
    public List<DocDocument> searchDocuments(String keyword, DocDocument docDocument);

    /**
     * 发布文档
     * 
     * @param docId 文档ID
     * @return 结果
     */
    public int publishDocument(Long docId);

    /**
     * 归档文档
     * 
     * @param docId 文档ID
     * @return 结果
     */
    public int archiveDocument(Long docId);

    /**
     * 获取文档版本历史
     * 
     * @param docId 文档ID
     * @return 版本列表
     */
    public List<DocVersion> getDocumentVersions(Long docId);

    /**
     * 版本回滚
     * 
     * @param docId 文档ID
     * @param versionId 版本ID
     * @return 结果
     */
    public int rollbackVersion(Long docId, Long versionId);

    /**
     * 分享文档
     * 
     * @param docId 文档ID
     * @param shareConfig 分享配置
     * @return 分享结果
     */
    public AjaxResult shareDocument(Long docId, String shareConfig);

    /**
     * 收藏/取消收藏文档
     * 
     * @param docId 文档ID
     * @return 结果
     */
    public int toggleFavorite(Long docId);

    /**
     * 获取收藏的文档
     * 
     * @param docDocument 查询条件
     * @return 文档集合
     */
    public List<DocDocument> getFavoriteDocuments(DocDocument docDocument);

    /**
     * 获取最近访问的文档
     * 
     * @param docDocument 查询条件
     * @return 文档集合
     */
    public List<DocDocument> getRecentDocuments(DocDocument docDocument);

    /**
     * 批量操作文档
     * 
     * @param operation 操作配置
     * @return 操作结果
     */
    public AjaxResult batchOperation(String operation);

    /**
     * 提取文档内容
     * 
     * @param filePath 文件路径
     * @param fileType 文件类型
     * @return 文档内容
     */
    public String extractDocumentContent(String filePath, String fileType);

    /**
     * 生成文档编号
     * 
     * @param categoryId 分类ID
     * @return 文档编号
     */
    public String generateDocumentCode(Long categoryId);
}
