-- ========================================
-- 文档管理系统数据库设计
-- 版本: 1.0
-- 创建时间: 2025-07-30
-- ========================================

USE `ry-vue`;

-- ========================================
-- 1. 文档分类表 (doc_category)
-- ========================================
DROP TABLE IF EXISTS `doc_category`;
CREATE TABLE `doc_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `ancestors` varchar(500) DEFAULT '' COMMENT '祖级列表',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) DEFAULT NULL COMMENT '分类编码',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `description` varchar(500) DEFAULT '' COMMENT '分类描述',
  `icon` varchar(100) DEFAULT '' COMMENT '分类图标',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_code` (`category_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档分类表';

-- ========================================
-- 2. 文档标签表 (doc_tag)
-- ========================================
DROP TABLE IF EXISTS `doc_tag`;
CREATE TABLE `doc_tag` (
  `tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_color` varchar(20) DEFAULT '#409EFF' COMMENT '标签颜色',
  `description` varchar(200) DEFAULT '' COMMENT '标签描述',
  `use_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`tag_id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档标签表';

-- ========================================
-- 3. 文档主表 (doc_document)
-- ========================================
DROP TABLE IF EXISTS `doc_document`;
CREATE TABLE `doc_document` (
  `doc_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `doc_title` varchar(200) NOT NULL COMMENT '文档标题',
  `doc_code` varchar(100) DEFAULT NULL COMMENT '文档编号',
  `doc_type` varchar(20) NOT NULL COMMENT '文档类型（word,pdf,excel,ppt,txt,md等）',
  `file_name` varchar(200) NOT NULL COMMENT '文件名称',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `file_md5` varchar(32) DEFAULT NULL COMMENT '文件MD5值',
  `content` longtext COMMENT '文档内容（用于全文检索）',
  `summary` varchar(1000) DEFAULT '' COMMENT '文档摘要',
  `keywords` varchar(500) DEFAULT '' COMMENT '关键词',
  `author` varchar(100) DEFAULT '' COMMENT '文档作者',
  `version_num` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `current_version_id` bigint(20) DEFAULT NULL COMMENT '当前版本ID',
  `publish_status` char(1) DEFAULT '0' COMMENT '发布状态（0草稿 1发布 2归档）',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `is_public` char(1) DEFAULT '0' COMMENT '是否公开（0私有 1公开）',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`doc_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_doc_code` (`doc_code`),
  KEY `idx_doc_type` (`doc_type`),
  KEY `idx_publish_status` (`publish_status`),
  KEY `idx_create_by` (`create_by`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_file_md5` (`file_md5`),
  FULLTEXT KEY `ft_content` (`content`,`doc_title`,`summary`,`keywords`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档主表';

-- ========================================
-- 4. 文档标签关联表 (doc_document_tag)
-- ========================================
DROP TABLE IF EXISTS `doc_document_tag`;
CREATE TABLE `doc_document_tag` (
  `doc_id` bigint(20) NOT NULL COMMENT '文档ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  PRIMARY KEY (`doc_id`,`tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档标签关联表';

-- ========================================
-- 5. 文档版本表 (doc_version)
-- ========================================
DROP TABLE IF EXISTS `doc_version`;
CREATE TABLE `doc_version` (
  `version_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `doc_id` bigint(20) NOT NULL COMMENT '文档ID',
  `version_num` varchar(20) NOT NULL COMMENT '版本号',
  `version_name` varchar(100) DEFAULT '' COMMENT '版本名称',
  `file_name` varchar(200) NOT NULL COMMENT '文件名称',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `file_md5` varchar(32) DEFAULT NULL COMMENT '文件MD5值',
  `content` longtext COMMENT '版本内容',
  `change_log` text COMMENT '变更日志',
  `is_current` char(1) DEFAULT '0' COMMENT '是否当前版本（0否 1是）',
  `publish_status` char(1) DEFAULT '0' COMMENT '发布状态（0草稿 1发布 2归档）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`version_id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_version_num` (`version_num`),
  KEY `idx_is_current` (`is_current`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档版本表';

-- ========================================
-- 6. 文档权限表 (doc_permission)
-- ========================================
DROP TABLE IF EXISTS `doc_permission`;
CREATE TABLE `doc_permission` (
  `permission_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `doc_id` bigint(20) NOT NULL COMMENT '文档ID',
  `permission_type` char(1) NOT NULL COMMENT '权限类型（1用户 2角色）',
  `permission_target` varchar(100) NOT NULL COMMENT '权限对象（用户ID或角色ID）',
  `permission_level` varchar(20) NOT NULL COMMENT '权限级别（read,edit,delete,download,share,admin）',
  `expire_time` datetime DEFAULT NULL COMMENT '权限过期时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`permission_id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_permission_type` (`permission_type`),
  KEY `idx_permission_target` (`permission_target`),
  KEY `idx_permission_level` (`permission_level`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档权限表';

-- ========================================
-- 7. 文档操作日志表 (doc_operation_log)
-- ========================================
DROP TABLE IF EXISTS `doc_operation_log`;
CREATE TABLE `doc_operation_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `doc_id` bigint(20) NOT NULL COMMENT '文档ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型（create,update,delete,view,download,share）',
  `operation_desc` varchar(200) DEFAULT '' COMMENT '操作描述',
  `user_id` bigint(20) DEFAULT NULL COMMENT '操作用户ID',
  `user_name` varchar(50) DEFAULT '' COMMENT '操作用户名',
  `ip_address` varchar(50) DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT '' COMMENT '用户代理',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`),
  KEY `idx_doc_id` (`doc_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='文档操作日志表';
