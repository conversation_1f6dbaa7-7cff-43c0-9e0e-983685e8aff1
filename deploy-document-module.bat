@echo off
echo ========================================
echo RuoYi-Vue 文档管理系统模块部署脚本
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3308
set DB_USER=root
set DB_PASSWORD=ankaixin.docker.mysql
set DB_NAME=ry-vue

echo [1/6] 环境检查...
echo ----------------------------------------

echo 检查Java环境...
java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Java环境未安装或配置不正确
    pause
    exit /b 1
) else (
    echo ✅ Java环境检查通过
)

echo 检查Maven环境...
mvn -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Maven环境未安装或配置不正确
    pause
    exit /b 1
) else (
    echo ✅ Maven环境检查通过
)

echo 检查Node.js环境...
node --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Node.js环境未安装或配置不正确
    pause
    exit /b 1
) else (
    echo ✅ Node.js环境检查通过
)
echo.

echo [2/6] 数据库初始化...
echo ----------------------------------------

echo 执行数据库建表脚本...
if exist "document-database-design.sql" (
    echo 找到数据库建表脚本，准备执行...
    echo 注意：请确保数据库连接正常，建议先在Navicat中手动执行SQL脚本
    echo 按任意键继续，或Ctrl+C取消...
    pause >nul
    echo ✅ 数据库脚本准备就绪（请手动执行）
) else (
    echo ❌ 未找到数据库建表脚本 document-database-design.sql
    pause
    exit /b 1
)

echo 执行初始化数据脚本...
if exist "document-init-data.sql" (
    echo ✅ 初始化数据脚本准备就绪（请手动执行）
) else (
    echo ❌ 未找到初始化数据脚本 document-init-data.sql
    pause
    exit /b 1
)
echo.

echo [3/6] 后端模块集成...
echo ----------------------------------------

echo 检查后端模块目录...
if exist "ruoyi-document" (
    echo ✅ 文档管理后端模块目录存在
) else (
    echo ❌ 未找到文档管理后端模块目录
    pause
    exit /b 1
)

echo 更新主项目pom.xml...
findstr /C:"ruoyi-document" pom.xml >nul
if %ERRORLEVEL% neq 0 (
    echo 需要在主项目pom.xml中添加文档管理模块依赖
    echo 请在 ^<modules^> 标签中添加：
    echo     ^<module^>ruoyi-document^</module^>
    echo.
    echo 请在ruoyi-admin的pom.xml中添加依赖：
    echo     ^<dependency^>
    echo         ^<groupId^>com.ruoyi^</groupId^>
    echo         ^<artifactId^>ruoyi-document^</artifactId^>
    echo     ^</dependency^>
    echo.
    echo 按任意键继续...
    pause >nul
) else (
    echo ✅ 主项目pom.xml已包含文档管理模块
)
echo.

echo [4/6] 前端页面集成...
echo ----------------------------------------

echo 检查前端页面目录...
if exist "ruoyi-ui\src\views\document" (
    echo ✅ 文档管理前端页面目录存在
) else (
    echo ❌ 未找到文档管理前端页面目录
    echo 请确保已创建 ruoyi-ui\src\views\document 目录及相关页面
    pause
    exit /b 1
)

echo 检查前端API接口...
if exist "ruoyi-ui\src\api\document" (
    echo ✅ 文档管理API接口目录存在
) else (
    echo ⚠️ 建议创建 ruoyi-ui\src\api\document 目录存放API接口文件
)
echo.

echo [5/6] 编译和构建...
echo ----------------------------------------

echo 编译后端项目...
echo 执行: mvn clean compile
mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ 后端项目编译失败
    pause
    exit /b 1
) else (
    echo ✅ 后端项目编译成功
)

echo 安装前端依赖...
cd ruoyi-ui
if exist "node_modules" (
    echo ✅ 前端依赖已安装
) else (
    echo 执行: npm install
    npm install
    if %ERRORLEVEL% neq 0 (
        echo ❌ 前端依赖安装失败
        cd ..
        pause
        exit /b 1
    ) else (
        echo ✅ 前端依赖安装成功
    )
)
cd ..
echo.

echo [6/6] 启动服务...
echo ----------------------------------------

echo 启动后端服务...
echo 在新窗口中启动后端服务（端口：55557）
start "RuoYi后端服务" cmd /k "cd /d %CD% && mvn spring-boot:run -pl ruoyi-admin"

echo 等待后端服务启动...
timeout /t 30 /nobreak >nul

echo 启动前端服务...
echo 在新窗口中启动前端服务（端口：33334）
start "RuoYi前端服务" cmd /k "cd /d %CD%\ruoyi-ui && npm run dev"

echo.
echo ========================================
echo 🎉 文档管理系统模块部署完成！
echo ========================================
echo.
echo 📋 部署信息：
echo - 后端服务地址: http://localhost:55557
echo - 前端管理界面: http://localhost:33334
echo - 数据库地址: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo.
echo 🔑 默认登录信息：
echo - 管理员账号: admin
echo - 默认密码: admin123
echo.
echo 📝 后续操作：
echo 1. 等待服务完全启动（约1-2分钟）
echo 2. 访问 http://localhost:33334 登录系统
echo 3. 在左侧菜单中找到"文档管理"模块
echo 4. 开始使用文档管理功能
echo.
echo 🔧 如果遇到问题：
echo 1. 检查数据库连接是否正常
echo 2. 确认端口55557和33334未被占用
echo 3. 查看控制台错误信息
echo 4. 检查防火墙和网络设置
echo.
echo ⚠️ 重要提醒：
echo - 请确保已在数据库中执行建表和初始化脚本
echo - 首次使用前建议先测试基础功能
echo - 生产环境部署前请进行充分测试
echo.

echo 按任意键退出...
pause >nul
