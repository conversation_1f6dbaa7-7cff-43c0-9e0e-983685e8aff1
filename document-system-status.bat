@echo off
echo ========================================
echo RuoYi-Vue 文档管理系统状态检查
echo ========================================
echo.

echo [1/5] 服务状态检查...
echo ----------------------------------------

echo 检查后端服务 (端口55557)...
powershell -Command "Test-NetConnection localhost -Port 55557 -InformationLevel Quiet" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ 后端服务运行正常
) else (
    echo ❌ 后端服务未运行
)

echo 检查前端服务 (端口33334)...
powershell -Command "Test-NetConnection localhost -Port 33334 -InformationLevel Quiet" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ 前端服务运行正常
) else (
    echo ❌ 前端服务未运行
)

echo 检查数据库连接 (端口3308)...
powershell -Command "Test-NetConnection localhost -Port 3308 -InformationLevel Quiet" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ 数据库连接正常
) else (
    echo ❌ 数据库连接失败
)
echo.

echo [2/5] 项目结构检查...
echo ----------------------------------------

if exist "ruoyi-document" (
    echo ✅ 文档管理后端模块存在
) else (
    echo ❌ 文档管理后端模块缺失
)

if exist "ruoyi-ui\src\views\document" (
    echo ✅ 文档管理前端页面存在
) else (
    echo ❌ 文档管理前端页面缺失
)

if exist "ruoyi-ui\src\api\document" (
    echo ✅ 文档管理API接口存在
) else (
    echo ❌ 文档管理API接口缺失
)
echo.

echo [3/5] 配置文件检查...
echo ----------------------------------------

findstr /C:"ruoyi-document" pom.xml >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ 主项目pom.xml已包含文档模块
) else (
    echo ❌ 主项目pom.xml未包含文档模块
)

findstr /C:"ruoyi-document" ruoyi-admin\pom.xml >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ admin模块已依赖文档模块
) else (
    echo ❌ admin模块未依赖文档模块
)
echo.

echo [4/5] API接口测试...
echo ----------------------------------------

echo 测试后端健康检查...
curl -s http://localhost:55557/actuator/health >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ 后端健康检查通过
) else (
    echo ⚠️ 后端健康检查失败（可能未配置actuator）
)

echo 测试前端页面访问...
curl -s http://localhost:33334 >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ 前端页面访问正常
) else (
    echo ❌ 前端页面访问失败
)
echo.

echo [5/5] 功能验证建议...
echo ----------------------------------------
echo 📋 请手动验证以下功能：
echo.
echo 1. 数据库初始化：
echo    - 在数据库管理工具中执行 execute-in-database.sql
echo    - 确认创建了 doc_category、doc_tag、doc_document 等表
echo    - 确认插入了初始化数据和菜单配置
echo.
echo 2. 系统登录验证：
echo    - 访问：http://localhost:33334
echo    - 使用账号：admin / admin123 登录
echo    - 检查左侧菜单是否显示"文档管理"
echo.
echo 3. 功能模块验证：
echo    - 文档列表：查看文档列表页面是否正常显示
echo    - 文档上传：测试文档上传功能
echo    - 分类管理：查看分类树形结构
echo    - 权限控制：确认菜单权限正常工作
echo.

echo ========================================
echo 系统状态检查完成！
echo ========================================
echo.
echo 🌐 访问地址：
echo - 前端管理界面: http://localhost:33334
echo - 后端API接口: http://localhost:55557
echo.
echo 🔑 默认登录：
echo - 用户名: admin
echo - 密码: admin123
echo.
echo 📝 下一步操作：
echo 1. 执行数据库初始化脚本
echo 2. 登录系统验证文档管理功能
echo 3. 测试文档上传和分类管理
echo.

pause
