@echo off
echo ========================================
echo RuoYi-Vue 文档管理系统自动初始化
echo ========================================
echo.

echo 正在尝试自动执行数据库初始化...
echo.

echo [步骤1] 创建数据库表结构...
echo ----------------------------------------

REM 尝试使用不同的MySQL客户端路径
set MYSQL_PATHS="C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" "C:\Program Files\MySQL\MySQL Server 5.7\bin\mysql.exe" "C:\xampp\mysql\bin\mysql.exe" "C:\wamp64\bin\mysql\mysql8.0.21\bin\mysql.exe" "mysql"

set MYSQL_FOUND=0
for %%i in (%MYSQL_PATHS%) do (
    if exist %%i (
        set MYSQL_CMD=%%i
        set MYSQL_FOUND=1
        echo ✅ 找到MySQL客户端: %%i
        goto :found_mysql
    )
)

REM 如果没有找到MySQL客户端，尝试直接使用mysql命令
mysql --version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    set MYSQL_CMD=mysql
    set MYSQL_FOUND=1
    echo ✅ 找到MySQL客户端: mysql (在PATH中)
    goto :found_mysql
)

:not_found_mysql
echo ❌ 未找到MySQL客户端
echo.
echo 📋 手动执行步骤：
echo 1. 在VS Code中打开Database扩展
echo 2. 连接到ry-vue数据库
echo 3. 依次执行以下SQL文件：
echo    - init-document-tables.sql  (创建表结构)
echo    - init-document-data.sql    (插入初始数据)
echo    - init-document-menu.sql    (配置菜单权限)
echo.
goto :end

:found_mysql
echo.
echo 使用MySQL客户端: %MYSQL_CMD%
echo 连接参数: localhost:3308, 用户: root, 数据库: ry-vue
echo.

echo 执行表结构创建...
%MYSQL_CMD% -h localhost -P 3308 -u root -pankaixin.docker.mysql -D ry-vue < init-document-tables.sql
if %ERRORLEVEL% equ 0 (
    echo ✅ 表结构创建成功
) else (
    echo ❌ 表结构创建失败
    goto :manual_steps
)

echo.
echo 执行初始数据插入...
%MYSQL_CMD% -h localhost -P 3308 -u root -pankaixin.docker.mysql -D ry-vue < init-document-data.sql
if %ERRORLEVEL% equ 0 (
    echo ✅ 初始数据插入成功
) else (
    echo ❌ 初始数据插入失败
    goto :manual_steps
)

echo.
echo 执行菜单权限配置...
%MYSQL_CMD% -h localhost -P 3308 -u root -pankaixin.docker.mysql -D ry-vue < init-document-menu.sql
if %ERRORLEVEL% equ 0 (
    echo ✅ 菜单权限配置成功
) else (
    echo ❌ 菜单权限配置失败
    goto :manual_steps
)

echo.
echo ========================================
echo 🎉 数据库初始化完成！
echo ========================================
echo.
echo ✅ 已创建的表：
echo    - doc_category (文档分类表)
echo    - doc_tag (文档标签表)
echo    - doc_document (文档主表)
echo    - doc_document_tag (文档标签关联表)
echo.
echo ✅ 已插入的数据：
echo    - 7个初始分类
echo    - 10个初始标签
echo    - 文档管理菜单配置
echo    - 权限分配
echo.
echo 🌐 现在可以访问系统：
echo    前端地址: http://localhost:33334
echo    登录账号: admin / admin123
echo.
goto :end

:manual_steps
echo.
echo ========================================
echo ⚠️ 自动执行失败，请手动执行
echo ========================================
echo.
echo 📋 手动执行步骤：
echo.
echo 1. 在VS Code中打开Database扩展
echo 2. 右键点击ry-vue数据库连接
echo 3. 选择"New Query"
echo 4. 依次复制并执行以下文件内容：
echo.
echo    📄 第一步：执行 init-document-tables.sql
echo       (创建doc_category, doc_tag, doc_document, doc_document_tag表)
echo.
echo    📄 第二步：执行 init-document-data.sql
echo       (插入初始分类和标签数据)
echo.
echo    📄 第三步：执行 init-document-menu.sql
echo       (配置文档管理菜单和权限)
echo.
echo 5. 执行完成后，重新登录系统验证功能
echo.

:end
echo 按任意键退出...
pause >nul
