import request from '@/utils/request'

// 查询文档标签列表
export function listTag(query) {
  return request({
    url: '/document/tag/list',
    method: 'get',
    params: query
  })
}

// 查询文档标签详细
export function getTag(tagId) {
  return request({
    url: '/document/tag/' + tagId,
    method: 'get'
  })
}

// 新增文档标签
export function addTag(data) {
  return request({
    url: '/document/tag',
    method: 'post',
    data: data
  })
}

// 修改文档标签
export function updateTag(data) {
  return request({
    url: '/document/tag',
    method: 'put',
    data: data
  })
}

// 删除文档标签
export function delTag(tagId) {
  return request({
    url: '/document/tag/' + tagId,
    method: 'delete'
  })
}

// 获取热门标签
export function getHotTags(limit) {
  return request({
    url: '/document/tag/hot',
    method: 'get',
    params: { limit: limit }
  })
}
