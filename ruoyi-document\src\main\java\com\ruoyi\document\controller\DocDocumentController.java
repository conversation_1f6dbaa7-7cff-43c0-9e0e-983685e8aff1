package com.ruoyi.document.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.document.domain.DocDocument;
import com.ruoyi.document.service.IDocDocumentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 文档管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequestMapping("/document/doc")
public class DocDocumentController extends BaseController
{
    @Autowired
    private IDocDocumentService docDocumentService;

    /**
     * 查询文档列表
     */
    @PreAuthorize("@ss.hasPermi('document:list:query')")
    @GetMapping("/list")
    public TableDataInfo list(DocDocument docDocument)
    {
        startPage();
        List<DocDocument> list = docDocumentService.selectDocDocumentList(docDocument);
        return getDataTable(list);
    }

    /**
     * 导出文档列表
     */
    @PreAuthorize("@ss.hasPermi('document:list:export')")
    @Log(title = "文档管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DocDocument docDocument)
    {
        List<DocDocument> list = docDocumentService.selectDocDocumentList(docDocument);
        ExcelUtil<DocDocument> util = new ExcelUtil<DocDocument>(DocDocument.class);
        util.exportExcel(response, list, "文档数据");
    }

    /**
     * 获取文档详细信息
     */
    @PreAuthorize("@ss.hasPermi('document:list:query')")
    @GetMapping(value = "/{docId}")
    public AjaxResult getInfo(@PathVariable("docId") Long docId)
    {
        return success(docDocumentService.selectDocDocumentByDocId(docId));
    }

    /**
     * 新增文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:add')")
    @Log(title = "文档管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DocDocument docDocument)
    {
        return toAjax(docDocumentService.insertDocDocument(docDocument));
    }

    /**
     * 修改文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:edit')")
    @Log(title = "文档管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DocDocument docDocument)
    {
        return toAjax(docDocumentService.updateDocDocument(docDocument));
    }

    /**
     * 删除文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:remove')")
    @Log(title = "文档管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{docIds}")
    public AjaxResult remove(@PathVariable Long[] docIds)
    {
        return toAjax(docDocumentService.deleteDocDocumentByDocIds(docIds));
    }

    /**
     * 上传文档
     */
    @PreAuthorize("@ss.hasPermi('document:upload:view')")
    @Log(title = "文档上传", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public AjaxResult uploadDocument(MultipartFile file, DocDocument docDocument)
    {
        try
        {
            return docDocumentService.uploadDocument(file, docDocument);
        }
        catch (Exception e)
        {
            return error("文档上传失败：" + e.getMessage());
        }
    }

    /**
     * 下载文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:download')")
    @Log(title = "文档下载", businessType = BusinessType.OTHER)
    @GetMapping("/download/{docId}")
    public void downloadDocument(@PathVariable Long docId, HttpServletResponse response)
    {
        try
        {
            docDocumentService.downloadDocument(docId, response);
        }
        catch (Exception e)
        {
            logger.error("文档下载失败", e);
        }
    }

    /**
     * 预览文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:query')")
    @GetMapping("/preview/{docId}")
    public AjaxResult previewDocument(@PathVariable Long docId)
    {
        try
        {
            return docDocumentService.previewDocument(docId);
        }
        catch (Exception e)
        {
            return error("文档预览失败：" + e.getMessage());
        }
    }

    /**
     * 文档全文搜索
     */
    @PreAuthorize("@ss.hasPermi('document:list:query')")
    @GetMapping("/search")
    public TableDataInfo searchDocuments(String keyword, DocDocument docDocument)
    {
        startPage();
        List<DocDocument> list = docDocumentService.searchDocuments(keyword, docDocument);
        return getDataTable(list);
    }

    /**
     * 发布文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:edit')")
    @Log(title = "文档发布", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{docId}")
    public AjaxResult publishDocument(@PathVariable Long docId)
    {
        return toAjax(docDocumentService.publishDocument(docId));
    }

    /**
     * 归档文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:edit')")
    @Log(title = "文档归档", businessType = BusinessType.UPDATE)
    @PutMapping("/archive/{docId}")
    public AjaxResult archiveDocument(@PathVariable Long docId)
    {
        return toAjax(docDocumentService.archiveDocument(docId));
    }

    /**
     * 获取文档版本历史
     */
    @PreAuthorize("@ss.hasPermi('document:list:query')")
    @GetMapping("/versions/{docId}")
    public AjaxResult getDocumentVersions(@PathVariable Long docId)
    {
        return success(docDocumentService.getDocumentVersions(docId));
    }

    /**
     * 版本回滚
     */
    @PreAuthorize("@ss.hasPermi('document:list:edit')")
    @Log(title = "文档版本回滚", businessType = BusinessType.UPDATE)
    @PutMapping("/rollback/{docId}/{versionId}")
    public AjaxResult rollbackVersion(@PathVariable Long docId, @PathVariable Long versionId)
    {
        return toAjax(docDocumentService.rollbackVersion(docId, versionId));
    }

    /**
     * 分享文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:share')")
    @Log(title = "文档分享", businessType = BusinessType.OTHER)
    @PostMapping("/share/{docId}")
    public AjaxResult shareDocument(@PathVariable Long docId, @RequestBody String shareConfig)
    {
        return docDocumentService.shareDocument(docId, shareConfig);
    }

    /**
     * 收藏/取消收藏文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:query')")
    @PostMapping("/favorite/{docId}")
    public AjaxResult toggleFavorite(@PathVariable Long docId)
    {
        return toAjax(docDocumentService.toggleFavorite(docId));
    }

    /**
     * 获取我的收藏文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:query')")
    @GetMapping("/favorites")
    public TableDataInfo getFavoriteDocuments(DocDocument docDocument)
    {
        startPage();
        List<DocDocument> list = docDocumentService.getFavoriteDocuments(docDocument);
        return getDataTable(list);
    }

    /**
     * 获取最近访问的文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:query')")
    @GetMapping("/recent")
    public TableDataInfo getRecentDocuments(DocDocument docDocument)
    {
        startPage();
        List<DocDocument> list = docDocumentService.getRecentDocuments(docDocument);
        return getDataTable(list);
    }

    /**
     * 批量操作文档
     */
    @PreAuthorize("@ss.hasPermi('document:list:edit')")
    @Log(title = "文档批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public AjaxResult batchOperation(@RequestBody String operation)
    {
        return docDocumentService.batchOperation(operation);
    }
}
