package com.ruoyi.document.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.document.domain.DocCategory;
import com.ruoyi.document.service.IDocCategoryService;

/**
 * 文档分类Controller
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequestMapping("/document/category")
public class DocCategoryController extends BaseController
{
    @Autowired
    private IDocCategoryService docCategoryService;

    /**
     * 查询文档分类列表
     */
    @PreAuthorize("@ss.hasPermi('document:category:view')")
    @GetMapping("/list")
    public AjaxResult list(DocCategory docCategory)
    {
        List<DocCategory> categories = docCategoryService.selectDocCategoryList(docCategory);
        return success(categories);
    }

    /**
     * 查询文档分类树列表
     */
    @PreAuthorize("@ss.hasPermi('document:category:view')")
    @GetMapping("/treeselect")
    public AjaxResult treeselect(DocCategory docCategory)
    {
        List<DocCategory> categories = docCategoryService.selectDocCategoryList(docCategory);
        return success(docCategoryService.buildCategoryTree(categories));
    }

    /**
     * 加载对应角色分类列表树
     */
    @PreAuthorize("@ss.hasPermi('document:category:view')")
    @GetMapping(value = "/roleTreeselect/{roleId}")
    public AjaxResult roleTreeselect(@PathVariable("roleId") Long roleId)
    {
        List<DocCategory> categories = docCategoryService.selectDocCategoryList(new DocCategory());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", docCategoryService.selectCategoryListByRoleId(roleId));
        ajax.put("categories", docCategoryService.buildCategoryTree(categories));
        return ajax;
    }

    /**
     * 获取文档分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('document:category:view')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        return success(docCategoryService.selectDocCategoryByCategoryId(categoryId));
    }

    /**
     * 获取分类下拉树列表
     */
    @PreAuthorize("@ss.hasPermi('document:category:view')")
    @GetMapping("/treeData")
    public List<Ztree> treeData()
    {
        List<DocCategory> categoryList = docCategoryService.selectDocCategoryList(new DocCategory());
        List<Ztree> ztrees = docCategoryService.initZtree(categoryList);
        return ztrees;
    }

    /**
     * 新增文档分类
     */
    @PreAuthorize("@ss.hasPermi('document:category:add')")
    @Log(title = "文档分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody DocCategory docCategory)
    {
        if ("1".equals(docCategoryService.checkCategoryNameUnique(docCategory)))
        {
            return error("新增分类'" + docCategory.getCategoryName() + "'失败，分类名称已存在");
        }
        return toAjax(docCategoryService.insertDocCategory(docCategory));
    }

    /**
     * 修改文档分类
     */
    @PreAuthorize("@ss.hasPermi('document:category:edit')")
    @Log(title = "文档分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody DocCategory docCategory)
    {
        Long categoryId = docCategory.getCategoryId();
        if (categoryId.equals(docCategory.getParentId()))
        {
            return error("修改分类'" + docCategory.getCategoryName() + "'失败，上级分类不能是自己");
        }
        else if (StringUtils.equals("1", docCategoryService.checkCategoryNameUnique(docCategory)))
        {
            return error("修改分类'" + docCategory.getCategoryName() + "'失败，分类名称已存在");
        }
        return toAjax(docCategoryService.updateDocCategory(docCategory));
    }

    /**
     * 删除文档分类
     */
    @PreAuthorize("@ss.hasPermi('document:category:remove')")
    @Log(title = "文档分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{categoryId}")
    public AjaxResult remove(@PathVariable("categoryId") Long categoryId)
    {
        if (docCategoryService.hasChildByCategoryId(categoryId))
        {
            return warn("存在子分类,不允许删除");
        }
        if (docCategoryService.checkCategoryExistDocument(categoryId))
        {
            return warn("分类存在文档,不允许删除");
        }
        return toAjax(docCategoryService.deleteDocCategoryByCategoryId(categoryId));
    }

    /**
     * 校验分类名称
     */
    @PreAuthorize("@ss.hasPermi('document:category:view')")
    @PostMapping("/checkCategoryNameUnique")
    public String checkCategoryNameUnique(DocCategory docCategory)
    {
        return docCategoryService.checkCategoryNameUnique(docCategory);
    }
}
