package com.ruoyi.document.service;

import java.util.List;
import com.ruoyi.document.domain.DocCategory;
import com.ruoyi.common.core.domain.Ztree;

/**
 * 文档分类Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IDocCategoryService 
{
    /**
     * 查询文档分类
     * 
     * @param categoryId 文档分类主键
     * @return 文档分类
     */
    public DocCategory selectDocCategoryByCategoryId(Long categoryId);

    /**
     * 查询文档分类列表
     * 
     * @param docCategory 文档分类
     * @return 文档分类集合
     */
    public List<DocCategory> selectDocCategoryList(DocCategory docCategory);

    /**
     * 构建前端所需要树结构
     * 
     * @param categories 分类列表
     * @return 树结构列表
     */
    public List<DocCategory> buildCategoryTree(List<DocCategory> categories);

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param categories 分类列表
     * @return 下拉树结构列表
     */
    public List<Ztree> initZtree(List<DocCategory> categories);

    /**
     * 新增文档分类
     * 
     * @param docCategory 文档分类
     * @return 结果
     */
    public int insertDocCategory(DocCategory docCategory);

    /**
     * 修改文档分类
     * 
     * @param docCategory 文档分类
     * @return 结果
     */
    public int updateDocCategory(DocCategory docCategory);

    /**
     * 批量删除文档分类
     * 
     * @param categoryIds 需要删除的文档分类主键集合
     * @return 结果
     */
    public int deleteDocCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 删除文档分类信息
     * 
     * @param categoryId 文档分类主键
     * @return 结果
     */
    public int deleteDocCategoryByCategoryId(Long categoryId);

    /**
     * 检查分类名称是否唯一
     * 
     * @param docCategory 分类信息
     * @return 结果
     */
    public String checkCategoryNameUnique(DocCategory docCategory);

    /**
     * 查询分类是否存在子分类
     * 
     * @param categoryId 分类ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean hasChildByCategoryId(Long categoryId);

    /**
     * 查询分类下是否存在文档
     * 
     * @param categoryId 分类ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkCategoryExistDocument(Long categoryId);

    /**
     * 根据角色ID查询分类权限
     * 
     * @param roleId 角色ID
     * @return 分类ID列表
     */
    public List<Long> selectCategoryListByRoleId(Long roleId);

    /**
     * 根据用户ID查询分类权限
     * 
     * @param userId 用户ID
     * @return 分类ID列表
     */
    public List<Long> selectCategoryListByUserId(Long userId);
}
