
@echo off
echo ========================================
echo RuoYi-Vue 数据库完全重建脚本
echo ========================================
echo.
echo ⚠️  警告：此操作将删除数据库中的所有表和数据！
echo ⚠️  请确保这是开发环境，不是生产环境！
echo.
echo 按任意键继续，或按Ctrl+C取消...
pause >nul
echo.

set DB_HOST=localhost
set DB_PORT=3308
set DB_USER=root
set DB_PASSWORD=ankaixin.docker.mysql
set DB_NAME=ry-vue

echo [1/6] 检查数据库连接...
echo ----------------------------------------
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ 错误：无法连接到MySQL数据库
    echo 请检查：
    echo - MySQL服务是否运行
    echo - 连接参数是否正确（主机：%DB_HOST%，端口：%DB_PORT%）
    echo - 用户名和密码是否正确
    pause
    exit /b 1
) else (
    echo ✅ 数据库连接成功
)
echo.

echo [2/6] 重置数据库（删除并重新创建）...
echo ----------------------------------------
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "DROP DATABASE IF EXISTS %DB_NAME%;"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;"
if %ERRORLEVEL% neq 0 (
    echo ❌ 错误：重置数据库失败
    pause
    exit /b 1
) else (
    echo ✅ 数据库重置成功
)
echo.

echo [3/6] 执行核心系统表脚本...
echo ----------------------------------------
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < sql/ry_20250522.sql
if %ERRORLEVEL% neq 0 (
    echo ❌ 错误：执行核心系统表脚本失败
    pause
    exit /b 1
) else (
    echo ✅ 核心系统表创建成功
)
echo.

echo [4/6] 执行Quartz定时任务表脚本...
echo ----------------------------------------
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < sql/quartz.sql
if %ERRORLEVEL% neq 0 (
    echo ❌ 错误：执行Quartz脚本失败
    pause
    exit /b 1
) else (
    echo ✅ Quartz定时任务表创建成功
)
echo.

echo [5/6] 执行文档管理表脚本...
echo ----------------------------------------
if exist "document-database-design.sql" (
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < document-database-design.sql
    if %ERRORLEVEL% neq 0 (
        echo ❌ 错误：执行文档管理表脚本失败
        pause
        exit /b 1
    ) else (
        echo ✅ 文档管理表创建成功
    )
) else (
    echo ⚠️  文档管理表脚本不存在，跳过
)
echo.

echo [6/6] 执行初始化数据脚本...
echo ----------------------------------------
if exist "document-init-data.sql" (
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < document-init-data.sql
    if %ERRORLEVEL% neq 0 (
        echo ❌ 错误：执行初始化数据脚本失败
        pause
        exit /b 1
    ) else (
        echo ✅ 初始化数据插入成功
    )
) else (
    echo ⚠️  初始化数据脚本不存在，跳过
)
echo.

echo ========================================
echo 🎉 数据库完全重建完成！
echo ========================================
echo.
echo ✅ 执行结果：
echo    - 数据库已完全重置
echo    - 核心系统表已创建
echo    - Quartz定时任务表已创建
echo    - 文档管理表已创建（如果脚本存在）
echo    - 初始化数据已插入（如果脚本存在）
echo.
echo 📊 数据库信息：
echo    - 主机：%DB_HOST%:%DB_PORT%
echo    - 数据库：%DB_NAME%
echo    - 用户：%DB_USER%
echo.
echo 🔑 默认管理员账号：
echo    - 用户名：admin
echo    - 密码：admin123
echo.
echo 🌐 现在可以启动系统：
echo    - 后端服务：运行 ry.bat 或在IDE中启动
echo    - 前端服务：在ruoyi-ui目录运行 npm run dev
echo    - 访问地址：http://localhost:33334
echo.

pause
