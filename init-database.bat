@echo off
echo ========================================
echo RuoYi-Vue 数据库初始化脚本
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3308
set DB_USER=root
set DB_PASSWORD=ankaixin.docker.mysql
set DB_NAME=ry-vue

echo [1/3] 检查数据库连接...
echo ----------------------------------------
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误：无法连接到MySQL数据库
    echo 请检查：
    echo - MySQL服务是否运行
    echo - 连接参数是否正确（主机：%DB_HOST%，端口：%DB_PORT%）
    echo - 用户名和密码是否正确
    pause
    exit /b 1
) else (
    echo 数据库连接成功
)
echo.

echo [2/3] 创建数据库（如果不存在）...
echo ----------------------------------------
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;"
if %ERRORLEVEL% neq 0 (
    echo 错误：创建数据库失败
    pause
    exit /b 1
) else (
    echo 数据库 %DB_NAME% 创建成功或已存在
)
echo.

echo [3/3] 执行SQL初始化脚本...
echo ----------------------------------------
echo 执行主数据库脚本...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < sql/ry_20250522.sql
if %ERRORLEVEL% neq 0 (
    echo 错误：执行主数据库脚本失败
    pause
    exit /b 1
) else (
    echo 主数据库脚本执行成功
)

echo 执行Quartz定时任务脚本...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < sql/quartz.sql
if %ERRORLEVEL% neq 0 (
    echo 错误：执行Quartz脚本失败
    pause
    exit /b 1
) else (
    echo Quartz脚本执行成功
)
echo.

echo ========================================
echo 数据库初始化完成！
echo ========================================
echo 数据库信息：
echo - 主机：%DB_HOST%:%DB_PORT%
echo - 数据库：%DB_NAME%
echo - 用户：%DB_USER%
echo.
echo 默认管理员账号：
echo - 用户名：admin
echo - 密码：admin123
echo.

pause
