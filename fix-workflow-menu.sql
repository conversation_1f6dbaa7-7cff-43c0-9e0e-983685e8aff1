-- 修复工作流菜单重复问题
USE `ry-vue`;

-- 1. 检查当前工作流菜单状态
SELECT '=== 当前工作流菜单状态 ===' AS info;
SELECT 
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    perms,
    status,
    visible
FROM sys_menu 
WHERE menu_name LIKE '%工作流%' 
   OR menu_name LIKE '%流程%' 
   OR menu_name LIKE '%任务%'
   OR menu_name LIKE '%设计器%'
   OR menu_name LIKE '%监控%'
   OR menu_name LIKE '%版本%'
   OR path LIKE '%workflow%'
ORDER BY parent_id, order_num;

-- 2. 检查是否存在重复的工作流管理主菜单
SELECT '=== 检查重复的工作流管理主菜单 ===' AS info;
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    status,
    visible,
    create_time
FROM sys_menu 
WHERE menu_name = '工作流管理' 
   OR path = 'workflow'
ORDER BY menu_id;

-- 3. 如果存在多个工作流管理菜单，保留ID最小的那个，删除其他的
-- 注意：这里只是查询，不执行删除操作
SELECT '=== 需要删除的重复菜单（如果存在） ===' AS info;
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    'DELETE FROM sys_menu WHERE menu_id = ' + CAST(menu_id AS CHAR) + ';' AS delete_sql
FROM sys_menu 
WHERE menu_name = '工作流管理' 
  AND menu_id NOT IN (
      SELECT MIN(menu_id) 
      FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '工作流管理') AS temp
  );

-- 4. 检查角色菜单权限
SELECT '=== 工作流菜单权限分配 ===' AS info;
SELECT 
    rm.role_id,
    r.role_name,
    rm.menu_id,
    m.menu_name,
    m.path
FROM sys_role_menu rm
JOIN sys_menu m ON rm.menu_id = m.menu_id
JOIN sys_role r ON rm.role_id = r.role_id
WHERE m.menu_name LIKE '%工作流%' 
   OR m.menu_name LIKE '%流程%' 
   OR m.menu_name LIKE '%任务%'
   OR m.path LIKE '%workflow%'
ORDER BY rm.role_id, m.menu_id;

-- 5. 确保工作流菜单数据完整性
SELECT '=== 工作流菜单数据完整性检查 ===' AS info;

-- 检查是否存在工作流管理主菜单
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '❌ 缺少工作流管理主菜单'
        WHEN COUNT(*) = 1 THEN '✅ 工作流管理主菜单正常'
        ELSE CONCAT('⚠️ 存在', COUNT(*), '个重复的工作流管理主菜单')
    END AS workflow_main_menu_status
FROM sys_menu 
WHERE menu_name = '工作流管理' AND parent_id = 0;

-- 检查工作流子菜单
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '❌ 缺少工作流子菜单'
        ELSE CONCAT('✅ 存在', COUNT(*), '个工作流子菜单')
    END AS workflow_sub_menu_status
FROM sys_menu 
WHERE parent_id IN (SELECT menu_id FROM sys_menu WHERE menu_name = '工作流管理');
